-- Analytics Tables for Skill Gap Analyzer
-- Migration: Add comprehensive analytics and telemetry tables

-- Main analytics events table
CREATE TABLE "AnalyticsEvent" (
    "id" TEXT NOT NULL,
    "eventName" TEXT NOT NULL,
    "userId" TEXT,
    "sessionId" TEXT NOT NULL,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "properties" JSONB NOT NULL DEFAULT '{}',
    "context" JSONB NOT NULL DEFAULT '{}',
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "AnalyticsEvent_pkey" PRIMARY KEY ("id")
);

-- User analytics summary table
CREATE TABLE "UserAnalytics" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "totalAssessments" INTEGER NOT NULL DEFAULT 0,
    "totalAnalyses" INTEGER NOT NULL DEFAULT 0,
    "totalSkillsAssessed" INTEGER NOT NULL DEFAULT 0,
    "averageSkillRating" DOUBLE PRECISION,
    "averageConfidenceLevel" DOUBLE PRECISION,
    "lastAssessmentDate" TIMESTAMP(3),
    "lastAssessmentCompletedDate" TIMESTAMP(3),
    "lastAnalysisDate" TIMESTAMP(3),
    "lastAnalysisId" TEXT,
    "totalSkillGaps" INTEGER NOT NULL DEFAULT 0,
    "criticalGapsCount" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserAnalytics_pkey" PRIMARY KEY ("id")
);

-- Skill gap analysis metrics table
CREATE TABLE "SkillGapAnalysisMetrics" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "analysisId" TEXT NOT NULL,
    "gapCount" INTEGER NOT NULL,
    "criticalGaps" INTEGER NOT NULL DEFAULT 0,
    "highGaps" INTEGER NOT NULL DEFAULT 0,
    "mediumGaps" INTEGER NOT NULL DEFAULT 0,
    "lowGaps" INTEGER NOT NULL DEFAULT 0,
    "processingTime" INTEGER NOT NULL, -- in milliseconds
    "aiServiceUsed" BOOLEAN NOT NULL DEFAULT false,
    "edgeCaseHandlerUsed" BOOLEAN NOT NULL DEFAULT false,
    "fallbackDataUsed" BOOLEAN NOT NULL DEFAULT false,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "SkillGapAnalysisMetrics_pkey" PRIMARY KEY ("id")
);

-- Learning resource interactions table
CREATE TABLE "LearningResourceInteraction" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "resourceTitle" TEXT NOT NULL,
    "resourceType" TEXT NOT NULL,
    "resourceUrl" TEXT NOT NULL,
    "context" TEXT NOT NULL, -- 'learning_plan', 'skill_gap', 'recommendation'
    "position" INTEGER NOT NULL,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "LearningResourceInteraction_pkey" PRIMARY KEY ("id")
);

-- Learning milestones table
CREATE TABLE "LearningMilestone" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "milestoneId" TEXT NOT NULL,
    "skillName" TEXT NOT NULL,
    "completedAt" TIMESTAMP(3) NOT NULL,
    "timeToComplete" INTEGER NOT NULL, -- in hours
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "LearningMilestone_pkey" PRIMARY KEY ("id")
);

-- Error analytics table
CREATE TABLE "ErrorAnalytics" (
    "id" TEXT NOT NULL,
    "userId" TEXT,
    "errorType" TEXT NOT NULL,
    "errorMessage" TEXT NOT NULL DEFAULT '',
    "context" TEXT NOT NULL,
    "recovered" BOOLEAN NOT NULL DEFAULT false,
    "fallbackUsed" BOOLEAN NOT NULL DEFAULT false,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ErrorAnalytics_pkey" PRIMARY KEY ("id")
);

-- Performance metrics table
CREATE TABLE "PerformanceMetric" (
    "id" TEXT NOT NULL,
    "userId" TEXT,
    "metricName" TEXT NOT NULL,
    "value" DOUBLE PRECISION NOT NULL,
    "context" TEXT NOT NULL,
    "threshold" DOUBLE PRECISION,
    "exceeded" BOOLEAN,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PerformanceMetric_pkey" PRIMARY KEY ("id")
);

-- Feature usage analytics table
CREATE TABLE "FeatureUsageAnalytics" (
    "id" TEXT NOT NULL,
    "userId" TEXT,
    "featureName" TEXT NOT NULL,
    "action" TEXT NOT NULL,
    "duration" INTEGER, -- in milliseconds
    "success" BOOLEAN NOT NULL DEFAULT true,
    "metadata" JSONB NOT NULL DEFAULT '{}',
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "FeatureUsageAnalytics_pkey" PRIMARY KEY ("id")
);

-- A/B test participation table
CREATE TABLE "ABTestParticipation" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "testName" TEXT NOT NULL,
    "variant" TEXT NOT NULL,
    "assignedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "convertedAt" TIMESTAMP(3),
    "conversionValue" DOUBLE PRECISION,
    "metadata" JSONB NOT NULL DEFAULT '{}',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ABTestParticipation_pkey" PRIMARY KEY ("id")
);

-- Create indexes for better query performance
CREATE INDEX "AnalyticsEvent_userId_idx" ON "AnalyticsEvent"("userId");
CREATE INDEX "AnalyticsEvent_eventName_idx" ON "AnalyticsEvent"("eventName");
CREATE INDEX "AnalyticsEvent_timestamp_idx" ON "AnalyticsEvent"("timestamp");
CREATE INDEX "AnalyticsEvent_sessionId_idx" ON "AnalyticsEvent"("sessionId");

CREATE UNIQUE INDEX "UserAnalytics_userId_key" ON "UserAnalytics"("userId");

CREATE INDEX "SkillGapAnalysisMetrics_userId_idx" ON "SkillGapAnalysisMetrics"("userId");
CREATE INDEX "SkillGapAnalysisMetrics_analysisId_idx" ON "SkillGapAnalysisMetrics"("analysisId");
CREATE INDEX "SkillGapAnalysisMetrics_timestamp_idx" ON "SkillGapAnalysisMetrics"("timestamp");

CREATE INDEX "LearningResourceInteraction_userId_idx" ON "LearningResourceInteraction"("userId");
CREATE INDEX "LearningResourceInteraction_resourceType_idx" ON "LearningResourceInteraction"("resourceType");
CREATE INDEX "LearningResourceInteraction_timestamp_idx" ON "LearningResourceInteraction"("timestamp");

CREATE INDEX "LearningMilestone_userId_idx" ON "LearningMilestone"("userId");
CREATE INDEX "LearningMilestone_skillName_idx" ON "LearningMilestone"("skillName");
CREATE INDEX "LearningMilestone_completedAt_idx" ON "LearningMilestone"("completedAt");

CREATE INDEX "ErrorAnalytics_userId_idx" ON "ErrorAnalytics"("userId");
CREATE INDEX "ErrorAnalytics_errorType_idx" ON "ErrorAnalytics"("errorType");
CREATE INDEX "ErrorAnalytics_timestamp_idx" ON "ErrorAnalytics"("timestamp");

CREATE INDEX "PerformanceMetric_userId_idx" ON "PerformanceMetric"("userId");
CREATE INDEX "PerformanceMetric_metricName_idx" ON "PerformanceMetric"("metricName");
CREATE INDEX "PerformanceMetric_timestamp_idx" ON "PerformanceMetric"("timestamp");

CREATE INDEX "FeatureUsageAnalytics_userId_idx" ON "FeatureUsageAnalytics"("userId");
CREATE INDEX "FeatureUsageAnalytics_featureName_idx" ON "FeatureUsageAnalytics"("featureName");
CREATE INDEX "FeatureUsageAnalytics_timestamp_idx" ON "FeatureUsageAnalytics"("timestamp");

CREATE INDEX "ABTestParticipation_userId_idx" ON "ABTestParticipation"("userId");
CREATE INDEX "ABTestParticipation_testName_idx" ON "ABTestParticipation"("testName");
CREATE UNIQUE INDEX "ABTestParticipation_userId_testName_key" ON "ABTestParticipation"("userId", "testName");

-- Add foreign key constraints
ALTER TABLE "AnalyticsEvent" ADD CONSTRAINT "AnalyticsEvent_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "UserAnalytics" ADD CONSTRAINT "UserAnalytics_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "SkillGapAnalysisMetrics" ADD CONSTRAINT "SkillGapAnalysisMetrics_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "LearningResourceInteraction" ADD CONSTRAINT "LearningResourceInteraction_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "LearningMilestone" ADD CONSTRAINT "LearningMilestone_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "ErrorAnalytics" ADD CONSTRAINT "ErrorAnalytics_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "PerformanceMetric" ADD CONSTRAINT "PerformanceMetric_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "FeatureUsageAnalytics" ADD CONSTRAINT "FeatureUsageAnalytics_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "ABTestParticipation" ADD CONSTRAINT "ABTestParticipation_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
