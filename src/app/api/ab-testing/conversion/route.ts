import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// A/B test conversion schema
const conversionSchema = z.object({
  userId: z.string(),
  testId: z.string(),
  variantId: z.string(),
  metricName: z.string(),
  value: z.number().optional(),
  convertedAt: z.string().transform(str => new Date(str))
});

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const body = await request.json();
    
    // Validate request body
    const validationResult = conversionSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid request format',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const { userId, testId, variantId, metricName, value, convertedAt } = validationResult.data;

    // Verify user authorization
    if (session?.user?.id !== userId) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Verify participation exists
    const participation = await prisma.aBTestParticipation.findUnique({
      where: {
        userId_testId: {
          userId,
          testId
        }
      }
    });

    if (!participation) {
      return NextResponse.json(
        { success: false, error: 'User not participating in this test' },
        { status: 404 }
      );
    }

    // Update participation with conversion data
    const updatedParticipation = await prisma.aBTestParticipation.update({
      where: {
        userId_testId: {
          userId,
          testId
        }
      },
      data: {
        convertedAt,
        conversionValue: value,
        metadata: {
          ...participation.metadata,
          conversions: {
            ...((participation.metadata as any)?.conversions || {}),
            [metricName]: {
              convertedAt: convertedAt.toISOString(),
              value
            }
          }
        }
      }
    });

    // Create conversion record for analytics
    await prisma.analyticsEvent.create({
      data: {
        eventName: 'ab_test_conversion',
        userId,
        sessionId: `ab_test_${testId}`,
        timestamp: convertedAt,
        properties: {
          testId,
          variantId,
          metricName,
          value
        },
        context: {
          userAgent: request.headers.get('user-agent') || '',
          url: request.headers.get('referer') || '',
          referrer: '',
          viewport: { width: 0, height: 0 },
          device: 'unknown',
          browser: 'unknown',
          os: 'unknown'
        }
      }
    });

    return NextResponse.json({
      success: true,
      participation: updatedParticipation,
      message: 'Conversion tracked successfully'
    });

  } catch (error) {
    console.error('A/B test conversion API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
