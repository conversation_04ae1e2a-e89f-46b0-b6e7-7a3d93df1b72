import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// A/B test participation schema
const participationSchema = z.object({
  userId: z.string(),
  testId: z.string(),
  variantId: z.string(),
  assignedAt: z.string().transform(str => new Date(str)),
  metadata: z.record(z.any()).default({})
});

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const body = await request.json();
    
    // Validate request body
    const validationResult = participationSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid request format',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const { userId, testId, variantId, assignedAt, metadata } = validationResult.data;

    // Verify user authorization
    if (session?.user?.id !== userId) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if participation already exists
    const existingParticipation = await prisma.aBTestParticipation.findUnique({
      where: {
        userId_testId: {
          userId,
          testId
        }
      }
    });

    if (existingParticipation) {
      return NextResponse.json({
        success: true,
        participation: existingParticipation,
        message: 'Participation already exists'
      });
    }

    // Create new participation record
    const participation = await prisma.aBTestParticipation.create({
      data: {
        userId,
        testName: testId,
        variant: variantId,
        assignedAt,
        metadata
      }
    });

    return NextResponse.json({
      success: true,
      participation
    });

  } catch (error) {
    console.error('A/B test participation API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const testId = searchParams.get('testId');
    const userId = session.user.id;

    // Build query
    const whereClause: any = { userId };
    if (testId) {
      whereClause.testName = testId;
    }

    // Get user's A/B test participations
    const participations = await prisma.aBTestParticipation.findMany({
      where: whereClause,
      orderBy: { assignedAt: 'desc' }
    });

    return NextResponse.json({
      success: true,
      participations
    });

  } catch (error) {
    console.error('A/B test participation GET API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
