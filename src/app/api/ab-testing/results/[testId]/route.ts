import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

interface ABTestResult {
  testId: string;
  variantId: string;
  participantCount: number;
  conversionRate: number;
  averageValue: number;
  confidenceInterval: {
    lower: number;
    upper: number;
    confidence: number;
  };
  statisticalSignificance: boolean;
  pValue: number;
}

export async function GET(
  request: NextRequest,
  { params }: { params: { testId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { testId } = params;
    const { searchParams } = new URL(request.url);
    const metricName = searchParams.get('metric') || 'primary';

    // Get all participations for this test
    const participations = await prisma.aBTestParticipation.findMany({
      where: {
        testName: testId
      }
    });

    if (participations.length === 0) {
      return NextResponse.json({
        success: true,
        results: [],
        message: 'No participants found for this test'
      });
    }

    // Group by variant
    const variantGroups = participations.reduce((groups, participation) => {
      const variant = participation.variant;
      if (!groups[variant]) {
        groups[variant] = [];
      }
      groups[variant].push(participation);
      return groups;
    }, {} as Record<string, typeof participations>);

    // Calculate results for each variant
    const results: ABTestResult[] = [];

    for (const [variantId, variantParticipations] of Object.entries(variantGroups)) {
      const participantCount = variantParticipations.length;
      
      // Calculate conversions based on metric
      let conversions = 0;
      let totalValue = 0;
      let valueCount = 0;

      variantParticipations.forEach(participation => {
        const metadata = participation.metadata as any;
        const conversionData = metadata?.conversions?.[metricName];
        
        if (conversionData) {
          conversions++;
          if (conversionData.value !== undefined) {
            totalValue += conversionData.value;
            valueCount++;
          }
        } else if (participation.convertedAt) {
          // Fallback to general conversion
          conversions++;
          if (participation.conversionValue !== undefined) {
            totalValue += participation.conversionValue;
            valueCount++;
          }
        }
      });

      const conversionRate = participantCount > 0 ? conversions / participantCount : 0;
      const averageValue = valueCount > 0 ? totalValue / valueCount : 0;

      // Calculate confidence interval (simplified)
      const confidenceInterval = calculateConfidenceInterval(
        conversions,
        participantCount,
        0.95
      );

      // Calculate statistical significance (simplified)
      const { pValue, isSignificant } = calculateStatisticalSignificance(
        variantGroups,
        variantId,
        metricName
      );

      results.push({
        testId,
        variantId,
        participantCount,
        conversionRate,
        averageValue,
        confidenceInterval,
        statisticalSignificance: isSignificant,
        pValue
      });
    }

    // Sort results by participant count (control first, then by size)
    results.sort((a, b) => {
      if (a.variantId === 'control') return -1;
      if (b.variantId === 'control') return 1;
      return b.participantCount - a.participantCount;
    });

    return NextResponse.json({
      success: true,
      results,
      summary: {
        totalParticipants: participations.length,
        variantCount: results.length,
        testId,
        metricName
      }
    });

  } catch (error) {
    console.error('A/B test results API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Calculate confidence interval for conversion rate
 */
function calculateConfidenceInterval(
  conversions: number,
  participants: number,
  confidence: number
): { lower: number; upper: number; confidence: number } {
  if (participants === 0) {
    return { lower: 0, upper: 0, confidence };
  }

  const p = conversions / participants;
  const z = getZScore(confidence);
  const margin = z * Math.sqrt((p * (1 - p)) / participants);

  return {
    lower: Math.max(0, p - margin),
    upper: Math.min(1, p + margin),
    confidence
  };
}

/**
 * Get Z-score for confidence level
 */
function getZScore(confidence: number): number {
  const zScores: Record<number, number> = {
    0.90: 1.645,
    0.95: 1.96,
    0.99: 2.576
  };
  return zScores[confidence] || 1.96;
}

/**
 * Calculate statistical significance (simplified chi-square test)
 */
function calculateStatisticalSignificance(
  variantGroups: Record<string, any[]>,
  currentVariant: string,
  metricName: string
): { pValue: number; isSignificant: boolean } {
  const variants = Object.keys(variantGroups);
  
  if (variants.length < 2) {
    return { pValue: 1, isSignificant: false };
  }

  // Find control group
  const controlVariant = variants.find(v => v === 'control') || variants[0];
  
  if (currentVariant === controlVariant) {
    return { pValue: 1, isSignificant: false };
  }

  const controlGroup = variantGroups[controlVariant];
  const testGroup = variantGroups[currentVariant];

  // Calculate conversion rates
  const controlConversions = controlGroup.filter(p => {
    const metadata = p.metadata as any;
    return metadata?.conversions?.[metricName] || p.convertedAt;
  }).length;

  const testConversions = testGroup.filter(p => {
    const metadata = p.metadata as any;
    return metadata?.conversions?.[metricName] || p.convertedAt;
  }).length;

  // Simplified z-test for proportions
  const p1 = controlConversions / controlGroup.length;
  const p2 = testConversions / testGroup.length;
  const n1 = controlGroup.length;
  const n2 = testGroup.length;

  if (n1 === 0 || n2 === 0) {
    return { pValue: 1, isSignificant: false };
  }

  const pooledP = (controlConversions + testConversions) / (n1 + n2);
  const se = Math.sqrt(pooledP * (1 - pooledP) * (1/n1 + 1/n2));
  
  if (se === 0) {
    return { pValue: 1, isSignificant: false };
  }

  const z = Math.abs(p2 - p1) / se;
  
  // Approximate p-value calculation (two-tailed)
  const pValue = 2 * (1 - normalCDF(Math.abs(z)));
  
  return {
    pValue,
    isSignificant: pValue < 0.05
  };
}

/**
 * Approximate normal cumulative distribution function
 */
function normalCDF(x: number): number {
  // Approximation using error function
  const a1 =  0.254829592;
  const a2 = -0.284496736;
  const a3 =  1.421413741;
  const a4 = -1.453152027;
  const a5 =  1.061405429;
  const p  =  0.3275911;

  const sign = x < 0 ? -1 : 1;
  x = Math.abs(x) / Math.sqrt(2.0);

  const t = 1.0 / (1.0 + p * x);
  const y = 1.0 - (((((a5 * t + a4) * t) + a3) * t + a2) * t + a1) * t * Math.exp(-x * x);

  return 0.5 * (1.0 + sign * y);
}
