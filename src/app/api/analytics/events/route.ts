import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Analytics event schema
const analyticsEventSchema = z.object({
  eventName: z.string(),
  userId: z.string().optional(),
  sessionId: z.string(),
  timestamp: z.string().transform(str => new Date(str)),
  properties: z.record(z.any()),
  context: z.object({
    userAgent: z.string(),
    url: z.string(),
    referrer: z.string(),
    viewport: z.object({
      width: z.number(),
      height: z.number()
    }),
    device: z.enum(['mobile', 'tablet', 'desktop']),
    browser: z.string(),
    os: z.string()
  })
});

const analyticsRequestSchema = z.object({
  events: z.array(analyticsEventSchema)
});

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const body = await request.json();
    
    // Validate request body
    const validationResult = analyticsRequestSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid request format',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const { events } = validationResult.data;

    // Process events in batches
    const processedEvents = await Promise.all(
      events.map(async (event) => {
        try {
          // Store event in database
          const storedEvent = await prisma.analyticsEvent.create({
            data: {
              eventName: event.eventName,
              userId: event.userId || session?.user?.id,
              sessionId: event.sessionId,
              timestamp: event.timestamp,
              properties: event.properties,
              context: event.context,
              // Additional metadata
              ipAddress: getClientIP(request),
              userAgent: request.headers.get('user-agent') || '',
            }
          });

          // Process specific skill gap analyzer events
          await processSkillGapEvent(event, session?.user?.id);

          return {
            eventId: storedEvent.id,
            status: 'processed'
          };
        } catch (error) {
          console.error('Error processing analytics event:', error);
          return {
            eventName: event.eventName,
            status: 'failed',
            error: error instanceof Error ? error.message : 'Unknown error'
          };
        }
      })
    );

    // Calculate success rate
    const successCount = processedEvents.filter(e => e.status === 'processed').length;
    const successRate = successCount / events.length;

    return NextResponse.json({
      success: true,
      processed: successCount,
      total: events.length,
      successRate,
      results: processedEvents
    });

  } catch (error) {
    console.error('Analytics API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Process skill gap analyzer specific events
 */
async function processSkillGapEvent(event: any, userId?: string): Promise<void> {
  if (!userId) return;

  try {
    switch (event.eventName) {
      case 'skill_assessment_started':
        await updateUserAnalytics(userId, {
          lastAssessmentDate: event.timestamp,
          totalAssessments: { increment: 1 }
        });
        break;

      case 'assessment_submitted':
        await updateUserAnalytics(userId, {
          lastAssessmentCompletedDate: event.timestamp,
          totalSkillsAssessed: event.properties.skillCount,
          averageSkillRating: event.properties.averageRating,
          averageConfidenceLevel: event.properties.averageConfidence
        });
        break;

      case 'gap_analysis_completed':
        await updateUserAnalytics(userId, {
          lastAnalysisDate: event.timestamp,
          totalAnalyses: { increment: 1 },
          lastAnalysisId: event.properties.analysisId,
          totalSkillGaps: event.properties.gapCount,
          criticalGapsCount: event.properties.criticalGaps
        });
        
        // Store analysis metrics
        await prisma.skillGapAnalysisMetrics.create({
          data: {
            userId,
            analysisId: event.properties.analysisId,
            gapCount: event.properties.gapCount,
            criticalGaps: event.properties.criticalGaps,
            highGaps: event.properties.highGaps,
            mediumGaps: event.properties.mediumGaps,
            lowGaps: event.properties.lowGaps,
            processingTime: event.properties.processingTime,
            aiServiceUsed: event.properties.aiServiceUsed,
            edgeCaseHandlerUsed: event.properties.edgeCaseHandlerUsed,
            fallbackDataUsed: event.properties.fallbackDataUsed,
            timestamp: event.timestamp
          }
        });
        break;

      case 'learning_resource_clicked':
        await prisma.learningResourceInteraction.create({
          data: {
            userId,
            resourceTitle: event.properties.resourceTitle,
            resourceType: event.properties.resourceType,
            resourceUrl: event.properties.resourceUrl,
            context: event.properties.context,
            position: event.properties.position,
            timestamp: event.timestamp
          }
        });
        break;

      case 'milestone_marked_complete':
        await prisma.learningMilestone.create({
          data: {
            userId,
            milestoneId: event.properties.milestoneId,
            skillName: event.properties.skillName,
            completedAt: event.timestamp,
            timeToComplete: event.properties.timeToComplete
          }
        });
        break;

      case 'error_encountered':
      case 'edge_case_triggered':
        await prisma.errorAnalytics.create({
          data: {
            userId,
            errorType: event.properties.errorType || event.properties.edgeCaseType,
            errorMessage: event.properties.errorMessage || '',
            context: event.properties.context,
            recovered: event.properties.recovered || event.properties.userRecovered,
            fallbackUsed: event.properties.fallbackUsed,
            timestamp: event.timestamp
          }
        });
        break;

      case 'performance_metric':
        await prisma.performanceMetric.create({
          data: {
            userId,
            metricName: event.properties.metricName,
            value: event.properties.value,
            context: event.properties.context,
            threshold: event.properties.threshold,
            exceeded: event.properties.exceeded,
            timestamp: event.timestamp
          }
        });
        break;
    }
  } catch (error) {
    console.error('Error processing skill gap event:', error);
    // Don't throw - we don't want to fail the entire batch for one event
  }
}

/**
 * Update user analytics summary
 */
async function updateUserAnalytics(userId: string, data: any): Promise<void> {
  await prisma.userAnalytics.upsert({
    where: { userId },
    update: data,
    create: {
      userId,
      ...data,
      totalAssessments: data.totalAssessments?.increment || 1,
      totalAnalyses: data.totalAnalyses?.increment || 1
    }
  });
}

/**
 * Get client IP address
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  return 'unknown';
}

// GET endpoint for analytics dashboard
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const timeframe = searchParams.get('timeframe') || '7d';
    const eventType = searchParams.get('eventType');

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    
    switch (timeframe) {
      case '1d':
        startDate.setDate(endDate.getDate() - 1);
        break;
      case '7d':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(endDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(endDate.getDate() - 90);
        break;
      default:
        startDate.setDate(endDate.getDate() - 7);
    }

    // Build query filters
    const whereClause: any = {
      userId: session.user.id,
      timestamp: {
        gte: startDate,
        lte: endDate
      }
    };

    if (eventType) {
      whereClause.eventName = eventType;
    }

    // Get analytics data
    const [events, userAnalytics, analysisMetrics, errorAnalytics] = await Promise.all([
      prisma.analyticsEvent.findMany({
        where: whereClause,
        orderBy: { timestamp: 'desc' },
        take: 100
      }),
      
      prisma.userAnalytics.findUnique({
        where: { userId: session.user.id }
      }),
      
      prisma.skillGapAnalysisMetrics.findMany({
        where: {
          userId: session.user.id,
          timestamp: {
            gte: startDate,
            lte: endDate
          }
        },
        orderBy: { timestamp: 'desc' }
      }),
      
      prisma.errorAnalytics.findMany({
        where: {
          userId: session.user.id,
          timestamp: {
            gte: startDate,
            lte: endDate
          }
        },
        orderBy: { timestamp: 'desc' }
      })
    ]);

    // Calculate summary statistics
    const eventCounts = events.reduce((counts: Record<string, number>, event) => {
      counts[event.eventName] = (counts[event.eventName] || 0) + 1;
      return counts;
    }, {});

    const avgProcessingTime = analysisMetrics.length > 0
      ? analysisMetrics.reduce((sum, metric) => sum + metric.processingTime, 0) / analysisMetrics.length
      : 0;

    const errorRate = events.length > 0
      ? errorAnalytics.length / events.length
      : 0;

    return NextResponse.json({
      success: true,
      data: {
        summary: {
          totalEvents: events.length,
          eventCounts,
          avgProcessingTime,
          errorRate,
          timeframe
        },
        userAnalytics,
        recentEvents: events.slice(0, 20),
        analysisMetrics,
        errorAnalytics: errorAnalytics.slice(0, 10)
      }
    });

  } catch (error) {
    console.error('Analytics GET API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
