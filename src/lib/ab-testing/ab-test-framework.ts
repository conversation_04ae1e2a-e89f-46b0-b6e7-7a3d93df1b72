/**
 * A/B Testing Framework for Skill Gap Analyzer
 * Provides feature flagging and experimentation capabilities
 */

import { skillGapAnalytics } from '@/lib/analytics/skill-gap-analytics';

// A/B Test Configuration
export interface ABTestConfig {
  testId: string;
  name: string;
  description: string;
  status: 'draft' | 'active' | 'paused' | 'completed';
  startDate: Date;
  endDate?: Date;
  targetAudience: {
    percentage: number; // 0-100
    userSegments?: string[];
    excludeUserIds?: string[];
    includeUserIds?: string[];
  };
  variants: ABTestVariant[];
  metrics: ABTestMetric[];
  hypothesis: string;
  successCriteria: string;
}

export interface ABTestVariant {
  id: string;
  name: string;
  description: string;
  trafficAllocation: number; // 0-100
  config: Record<string, any>;
  isControl: boolean;
}

export interface ABTestMetric {
  name: string;
  type: 'conversion' | 'numeric' | 'duration';
  primaryMetric: boolean;
  description: string;
  targetValue?: number;
  targetDirection: 'increase' | 'decrease';
}

export interface ABTestParticipation {
  userId: string;
  testId: string;
  variantId: string;
  assignedAt: Date;
  convertedAt?: Date;
  conversionValue?: number;
  metadata: Record<string, any>;
}

export interface ABTestResult {
  testId: string;
  variantId: string;
  participantCount: number;
  conversionRate: number;
  averageValue: number;
  confidenceInterval: {
    lower: number;
    upper: number;
    confidence: number;
  };
  statisticalSignificance: boolean;
  pValue: number;
}

// Predefined A/B tests for Skill Gap Analyzer
export const SKILL_GAP_AB_TESTS: Record<string, ABTestConfig> = {
  'skill-assessment-ui': {
    testId: 'skill-assessment-ui',
    name: 'Skill Assessment UI Optimization',
    description: 'Test different UI layouts for skill assessment form',
    status: 'active',
    startDate: new Date('2025-01-01'),
    endDate: new Date('2025-02-01'),
    targetAudience: {
      percentage: 50,
      userSegments: ['new_users']
    },
    variants: [
      {
        id: 'control',
        name: 'Current UI',
        description: 'Existing skill assessment interface',
        trafficAllocation: 50,
        config: {
          layout: 'vertical',
          skillsPerPage: 5,
          showProgressBar: true
        },
        isControl: true
      },
      {
        id: 'horizontal-layout',
        name: 'Horizontal Layout',
        description: 'Horizontal skill rating layout with visual improvements',
        trafficAllocation: 50,
        config: {
          layout: 'horizontal',
          skillsPerPage: 3,
          showProgressBar: true,
          visualEnhancements: true
        },
        isControl: false
      }
    ],
    metrics: [
      {
        name: 'assessment_completion_rate',
        type: 'conversion',
        primaryMetric: true,
        description: 'Percentage of users who complete the skill assessment',
        targetDirection: 'increase'
      },
      {
        name: 'time_to_complete',
        type: 'duration',
        primaryMetric: false,
        description: 'Time taken to complete assessment',
        targetDirection: 'decrease'
      }
    ],
    hypothesis: 'Horizontal layout will improve completion rates by making the interface more intuitive',
    successCriteria: 'Increase completion rate by 10% with statistical significance'
  },

  'analysis-results-display': {
    testId: 'analysis-results-display',
    name: 'Analysis Results Display',
    description: 'Test different ways to present skill gap analysis results',
    status: 'active',
    startDate: new Date('2025-01-01'),
    targetAudience: {
      percentage: 30,
      userSegments: ['returning_users']
    },
    variants: [
      {
        id: 'control',
        name: 'Tabbed Interface',
        description: 'Current tabbed results display',
        trafficAllocation: 50,
        config: {
          displayType: 'tabs',
          showSummaryFirst: false
        },
        isControl: true
      },
      {
        id: 'summary-first',
        name: 'Summary First',
        description: 'Show executive summary before detailed results',
        trafficAllocation: 50,
        config: {
          displayType: 'summary-first',
          showSummaryFirst: true,
          summaryCards: true
        },
        isControl: false
      }
    ],
    metrics: [
      {
        name: 'results_engagement',
        type: 'numeric',
        primaryMetric: true,
        description: 'Time spent viewing results',
        targetDirection: 'increase'
      },
      {
        name: 'learning_plan_clicks',
        type: 'conversion',
        primaryMetric: false,
        description: 'Users who click on learning resources',
        targetDirection: 'increase'
      }
    ],
    hypothesis: 'Summary-first approach will increase user engagement with results',
    successCriteria: 'Increase engagement time by 15%'
  },

  'edge-case-error-messages': {
    testId: 'edge-case-error-messages',
    name: 'Edge Case Error Messages',
    description: 'Test different error message styles for edge cases',
    status: 'active',
    startDate: new Date('2025-01-01'),
    targetAudience: {
      percentage: 100 // All users experiencing errors
    },
    variants: [
      {
        id: 'control',
        name: 'Technical Messages',
        description: 'Current technical error messages',
        trafficAllocation: 50,
        config: {
          messageStyle: 'technical',
          showTechnicalDetails: true
        },
        isControl: true
      },
      {
        id: 'user-friendly',
        name: 'User-Friendly Messages',
        description: 'Simplified, action-oriented error messages',
        trafficAllocation: 50,
        config: {
          messageStyle: 'user-friendly',
          showTechnicalDetails: false,
          showSuggestedActions: true
        },
        isControl: false
      }
    ],
    metrics: [
      {
        name: 'error_recovery_rate',
        type: 'conversion',
        primaryMetric: true,
        description: 'Users who successfully recover from errors',
        targetDirection: 'increase'
      },
      {
        name: 'retry_attempts',
        type: 'numeric',
        primaryMetric: false,
        description: 'Number of retry attempts before success',
        targetDirection: 'decrease'
      }
    ],
    hypothesis: 'User-friendly error messages will improve error recovery rates',
    successCriteria: 'Increase recovery rate by 20%'
  }
};

export class ABTestFramework {
  private participations: Map<string, ABTestParticipation> = new Map();
  private activeTests: Map<string, ABTestConfig> = new Map();

  constructor() {
    this.loadActiveTests();
  }

  /**
   * Load active A/B tests
   */
  private loadActiveTests(): void {
    Object.values(SKILL_GAP_AB_TESTS).forEach(test => {
      if (test.status === 'active') {
        this.activeTests.set(test.testId, test);
      }
    });
  }

  /**
   * Get variant for a user in a specific test
   */
  async getVariant(testId: string, userId: string, userSegments: string[] = []): Promise<string | null> {
    const test = this.activeTests.get(testId);
    if (!test) {
      return null;
    }

    // Check if test is active
    if (!this.isTestActive(test)) {
      return null;
    }

    // Check if user is in target audience
    if (!this.isUserInTargetAudience(test, userId, userSegments)) {
      return null;
    }

    // Check existing participation
    const participationKey = `${userId}-${testId}`;
    let participation = this.participations.get(participationKey);

    if (participation) {
      return participation.variantId;
    }

    // Assign user to variant
    const variantId = this.assignUserToVariant(test, userId);
    if (!variantId) {
      return null;
    }

    // Record participation
    participation = {
      userId,
      testId,
      variantId,
      assignedAt: new Date(),
      metadata: {}
    };

    this.participations.set(participationKey, participation);

    // Track assignment
    skillGapAnalytics.track('ab_test_assigned' as any, {
      userId,
      testId,
      variantId,
      testName: test.name
    });

    // Store in database
    await this.storeParticipation(participation);

    return variantId;
  }

  /**
   * Get test configuration for a variant
   */
  getVariantConfig(testId: string, variantId: string): Record<string, any> | null {
    const test = this.activeTests.get(testId);
    if (!test) {
      return null;
    }

    const variant = test.variants.find(v => v.id === variantId);
    return variant?.config || null;
  }

  /**
   * Track conversion for a test
   */
  async trackConversion(
    testId: string, 
    userId: string, 
    metricName: string, 
    value?: number
  ): Promise<void> {
    const participationKey = `${userId}-${testId}`;
    const participation = this.participations.get(participationKey);

    if (!participation) {
      return; // User not in test
    }

    const test = this.activeTests.get(testId);
    if (!test) {
      return;
    }

    const metric = test.metrics.find(m => m.name === metricName);
    if (!metric) {
      return;
    }

    // Update participation
    if (!participation.convertedAt) {
      participation.convertedAt = new Date();
      participation.conversionValue = value;
    }

    // Track conversion
    skillGapAnalytics.track('ab_test_conversion' as any, {
      userId,
      testId,
      variantId: participation.variantId,
      metricName,
      value,
      testName: test.name
    });

    // Store conversion
    await this.storeConversion(participation, metricName, value);
  }

  /**
   * Check if test is currently active
   */
  private isTestActive(test: ABTestConfig): boolean {
    const now = new Date();
    const isAfterStart = now >= test.startDate;
    const isBeforeEnd = !test.endDate || now <= test.endDate;
    
    return test.status === 'active' && isAfterStart && isBeforeEnd;
  }

  /**
   * Check if user is in target audience
   */
  private isUserInTargetAudience(
    test: ABTestConfig, 
    userId: string, 
    userSegments: string[]
  ): boolean {
    const { targetAudience } = test;

    // Check explicit exclusions
    if (targetAudience.excludeUserIds?.includes(userId)) {
      return false;
    }

    // Check explicit inclusions
    if (targetAudience.includeUserIds?.includes(userId)) {
      return true;
    }

    // Check user segments
    if (targetAudience.userSegments) {
      const hasMatchingSegment = targetAudience.userSegments.some(segment => 
        userSegments.includes(segment)
      );
      if (!hasMatchingSegment) {
        return false;
      }
    }

    // Check percentage rollout
    const userHash = this.hashUserId(userId, test.testId);
    const userPercentile = userHash % 100;
    
    return userPercentile < targetAudience.percentage;
  }

  /**
   * Assign user to a variant based on traffic allocation
   */
  private assignUserToVariant(test: ABTestConfig, userId: string): string | null {
    const userHash = this.hashUserId(userId, test.testId + '_variant');
    const userPercentile = userHash % 100;

    let cumulativeAllocation = 0;
    for (const variant of test.variants) {
      cumulativeAllocation += variant.trafficAllocation;
      if (userPercentile < cumulativeAllocation) {
        return variant.id;
      }
    }

    return null; // Should not happen if allocations sum to 100
  }

  /**
   * Hash user ID for consistent assignment
   */
  private hashUserId(userId: string, salt: string): number {
    const str = userId + salt;
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  /**
   * Store participation in database
   */
  private async storeParticipation(participation: ABTestParticipation): Promise<void> {
    try {
      await fetch('/api/ab-testing/participation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(participation)
      });
    } catch (error) {
      console.error('Failed to store A/B test participation:', error);
    }
  }

  /**
   * Store conversion in database
   */
  private async storeConversion(
    participation: ABTestParticipation, 
    metricName: string, 
    value?: number
  ): Promise<void> {
    try {
      await fetch('/api/ab-testing/conversion', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: participation.userId,
          testId: participation.testId,
          variantId: participation.variantId,
          metricName,
          value,
          convertedAt: participation.convertedAt
        })
      });
    } catch (error) {
      console.error('Failed to store A/B test conversion:', error);
    }
  }

  /**
   * Get test results
   */
  async getTestResults(testId: string): Promise<ABTestResult[]> {
    try {
      const response = await fetch(`/api/ab-testing/results/${testId}`);
      if (response.ok) {
        const data = await response.json();
        return data.results;
      }
    } catch (error) {
      console.error('Failed to fetch test results:', error);
    }
    return [];
  }

  /**
   * End a test
   */
  async endTest(testId: string): Promise<void> {
    const test = this.activeTests.get(testId);
    if (test) {
      test.status = 'completed';
      test.endDate = new Date();
      this.activeTests.delete(testId);

      // Store updated test config
      await fetch(`/api/ab-testing/tests/${testId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'completed', endDate: test.endDate })
      });
    }
  }
}

// Global A/B testing instance
export const abTestFramework = new ABTestFramework();
