'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAccessibility } from '@/components/accessibility/AccessibilityProvider';
import { 
  TrendingUp, 
  TrendingDown, 
  Target, 
  Clock, 
  BookOpen, 
  Award, 
  AlertTriangle,
  CheckCircle,
  BarChart3,
  Lightbulb,
  Calendar,
  DollarSign
} from 'lucide-react';

// Types (same as original)
interface SkillGap {
  skillId: string;
  skillName: string;
  currentLevel: number;
  targetLevel: number;
  gapSeverity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  marketDemand: 'LOW' | 'MEDIUM' | 'HIGH';
  priority: number;
  estimatedLearningTime: number;
  description?: string;
}

interface LearningPlan {
  totalEstimatedHours: number;
  phases: Array<{
    phase: number;
    title: string;
    duration: number;
    skills: string[];
    milestones: string[];
  }>;
  milestones: string[];
  recommendedResources: Array<{
    title: string;
    type: string;
    url: string;
    estimatedHours: number;
    difficulty: string;
  }>;
}

interface CareerReadiness {
  currentScore: number;
  targetScore: number;
  improvementPotential: number;
  timeToTarget: number;
  readinessLevel: string;
  strengthAreas: string[];
  improvementAreas: string[];
}

interface MarketInsights {
  skillDemandTrends: Record<string, { trend: string; demandScore: number }>;
  salaryProjections: {
    currentRange: { min: number; max: number };
    targetRange: { min: number; max: number };
  };
  jobMarketData: {
    openPositions: number;
    competitionLevel: string;
    hiringTrends: string;
  };
}

interface AccessibleSkillGapAnalysisProps {
  analysisId: string;
  skillGaps: SkillGap[];
  learningPlan: LearningPlan;
  careerReadiness: CareerReadiness;
  marketInsights?: MarketInsights;
  generatedAt: string;
  cached?: boolean;
  edgeCaseHandlerData?: {
    sanitizedInput?: any;
    isNewUser?: boolean;
    onboardingRecommendations?: string[];
    retryCount?: number;
    fallbackDataUsed?: boolean;
    suggestedAlternatives?: string[];
  };
}

export default function AccessibleSkillGapAnalysis({
  analysisId,
  skillGaps,
  learningPlan,
  careerReadiness,
  marketInsights,
  generatedAt,
  cached = false,
  edgeCaseHandlerData,
}: AccessibleSkillGapAnalysisProps) {
  const [selectedTab, setSelectedTab] = useState('overview');
  const { announceToScreenReader, settings } = useAccessibility();
  const tabsRef = useRef<HTMLDivElement>(null);
  const overviewRef = useRef<HTMLDivElement>(null);

  // Announce tab changes to screen readers
  useEffect(() => {
    const tabNames = {
      overview: 'Overview',
      gaps: 'Skill Gaps',
      plan: 'Learning Plan',
      market: 'Market Insights'
    };
    
    announceToScreenReader(`Switched to ${tabNames[selectedTab as keyof typeof tabNames]} tab`);
  }, [selectedTab, announceToScreenReader]);

  // Announce analysis completion
  useEffect(() => {
    announceToScreenReader(
      `Skill gap analysis completed. Found ${skillGaps.length} skill gaps with ${
        skillGaps.filter(gap => gap.gapSeverity === 'CRITICAL').length
      } critical gaps requiring immediate attention.`
    );
  }, [skillGaps, announceToScreenReader]);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return 'bg-red-500';
      case 'HIGH': return 'bg-orange-500';
      case 'MEDIUM': return 'bg-yellow-500';
      case 'LOW': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getSeverityLabel = (severity: string) => {
    const labels = {
      'CRITICAL': 'Critical priority - immediate attention required',
      'HIGH': 'High priority - address soon',
      'MEDIUM': 'Medium priority - plan for development',
      'LOW': 'Low priority - consider for future development'
    };
    return labels[severity as keyof typeof labels] || severity;
  };

  const getDemandColor = (demand: string) => {
    switch (demand) {
      case 'HIGH': return 'text-green-600 border-green-600';
      case 'MEDIUM': return 'text-yellow-600 border-yellow-600';
      case 'LOW': return 'text-red-600 border-red-600';
      default: return 'text-gray-600 border-gray-600';
    }
  };

  const formatHours = (hours: number) => {
    if (hours < 1) return `${Math.round(hours * 60)} minutes`;
    if (hours < 24) return `${Math.round(hours)} hours`;
    return `${Math.round(hours / 24)} days`;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const handleTabChange = (value: string) => {
    setSelectedTab(value);
    
    // Focus management for keyboard users
    setTimeout(() => {
      const tabPanel = document.querySelector(`[data-state="active"][role="tabpanel"]`);
      if (tabPanel) {
        (tabPanel as HTMLElement).focus();
      }
    }, 100);
  };

  return (
    <div className="space-y-6" role="main" aria-labelledby="analysis-title">
      {/* Header with improved accessibility */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle id="analysis-title" className="flex items-center gap-2">
                <Target className="h-5 w-5" aria-hidden="true" />
                Skill Gap Analysis Results
              </CardTitle>
              <CardDescription>
                Analysis generated on {new Date(generatedAt).toLocaleDateString()}
                {cached && <span className="sr-only">. Results loaded from cache.</span>}
                <div className="flex gap-2 mt-1" role="group" aria-label="Analysis status indicators">
                  {cached && (
                    <Badge variant="secondary" aria-label="Results from cache">
                      Cached
                    </Badge>
                  )}
                  {edgeCaseHandlerData?.fallbackDataUsed && (
                    <Badge 
                      variant="outline" 
                      className="text-orange-600 border-orange-600"
                      aria-label="Limited data available due to service issues"
                    >
                      Fallback Data
                    </Badge>
                  )}
                  {edgeCaseHandlerData?.isNewUser && (
                    <Badge 
                      variant="outline" 
                      className="text-blue-600 border-blue-600"
                      aria-label="New user - recommendations may be limited"
                    >
                      New User
                    </Badge>
                  )}
                </div>
              </CardDescription>
            </div>
            
            {/* Quick stats for screen readers */}
            <div className="text-right">
              <div className="text-2xl font-bold" aria-label={`Career readiness score: ${careerReadiness.currentScore} out of ${careerReadiness.targetScore}`}>
                {careerReadiness.currentScore}/{careerReadiness.targetScore}
              </div>
              <div className="text-sm text-gray-500">Career Readiness</div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Onboarding recommendations for new users */}
      {edgeCaseHandlerData?.isNewUser && edgeCaseHandlerData.onboardingRecommendations && (
        <Alert role="region" aria-labelledby="onboarding-title">
          <Lightbulb className="h-4 w-4" aria-hidden="true" />
          <div>
            <h3 id="onboarding-title" className="font-medium">Welcome! Here are some tips to get started:</h3>
            <AlertDescription>
              <ul className="mt-2 space-y-1" role="list">
                {edgeCaseHandlerData.onboardingRecommendations.map((tip, index) => (
                  <li key={index} role="listitem">• {tip}</li>
                ))}
              </ul>
            </AlertDescription>
          </div>
        </Alert>
      )}

      {/* Suggested Alternatives with improved accessibility */}
      {edgeCaseHandlerData?.suggestedAlternatives && edgeCaseHandlerData.suggestedAlternatives.length > 0 && (
        <Card className="border-yellow-200 bg-yellow-50" role="region" aria-labelledby="alternatives-title">
          <CardHeader>
            <CardTitle id="alternatives-title" className="flex items-center gap-2 text-yellow-700">
              <AlertTriangle className="h-5 w-5" aria-hidden="true" />
              Alternative Recommendations
            </CardTitle>
            <CardDescription className="text-yellow-600">
              Based on your profile, you might also consider these alternative skills or career paths.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2" role="group" aria-label="Alternative skill recommendations">
              {edgeCaseHandlerData.suggestedAlternatives.map((alternative, index) => (
                <Badge 
                  key={index} 
                  variant="outline" 
                  className="text-yellow-700 border-yellow-700"
                  tabIndex={0}
                  role="button"
                  aria-label={`Alternative recommendation: ${typeof alternative === 'string' ? alternative : alternative.name || alternative.title}`}
                >
                  {typeof alternative === 'string' ? alternative : alternative.name || alternative.title}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Accessible Tabs */}
      <Tabs value={selectedTab} onValueChange={handleTabChange} ref={tabsRef}>
        <TabsList className="grid w-full grid-cols-4" role="tablist" aria-label="Analysis sections">
          <TabsTrigger 
            value="overview" 
            role="tab"
            aria-controls="overview-panel"
            aria-selected={selectedTab === 'overview'}
          >
            Overview
          </TabsTrigger>
          <TabsTrigger 
            value="gaps" 
            role="tab"
            aria-controls="gaps-panel"
            aria-selected={selectedTab === 'gaps'}
          >
            Skill Gaps ({skillGaps.length})
          </TabsTrigger>
          <TabsTrigger 
            value="plan" 
            role="tab"
            aria-controls="plan-panel"
            aria-selected={selectedTab === 'plan'}
          >
            Learning Plan
          </TabsTrigger>
          <TabsTrigger 
            value="market" 
            role="tab"
            aria-controls="market-panel"
            aria-selected={selectedTab === 'market'}
          >
            Market Insights
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent 
          value="overview" 
          className="space-y-6"
          role="tabpanel"
          id="overview-panel"
          aria-labelledby="overview-tab"
          tabIndex={-1}
          ref={overviewRef}
        >
          <h2 className="sr-only">Analysis Overview</h2>
          
          {/* Career Readiness Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5" aria-hidden="true" />
                Career Readiness Assessment
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span>Current Score</span>
                <span className="text-2xl font-bold" aria-label={`${careerReadiness.currentScore} out of ${careerReadiness.targetScore}`}>
                  {careerReadiness.currentScore}/{careerReadiness.targetScore}
                </span>
              </div>
              
              <Progress 
                value={(careerReadiness.currentScore / careerReadiness.targetScore) * 100} 
                className="h-3"
                aria-label={`Career readiness progress: ${Math.round((careerReadiness.currentScore / careerReadiness.targetScore) * 100)}% complete`}
              />
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <div className="text-gray-500">Readiness Level</div>
                  <div className="font-medium">{careerReadiness.readinessLevel}</div>
                </div>
                <div>
                  <div className="text-gray-500">Improvement Potential</div>
                  <div className="font-medium">{careerReadiness.improvementPotential}%</div>
                </div>
                <div>
                  <div className="text-gray-500">Time to Target</div>
                  <div className="font-medium">{careerReadiness.timeToTarget} months</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Critical Gaps Alert */}
          {skillGaps.some(gap => gap.gapSeverity === 'CRITICAL') && (
            <Alert role="alert" aria-live="assertive">
              <AlertTriangle className="h-4 w-4" aria-hidden="true" />
              <AlertDescription>
                <strong>Attention required:</strong> You have {skillGaps.filter(gap => gap.gapSeverity === 'CRITICAL').length} critical skill gaps 
                that require immediate attention for your career goals.
              </AlertDescription>
            </Alert>
          )}

          {/* Quick Summary */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-500">Total Skill Gaps</p>
                    <p className="text-2xl font-bold">{skillGaps.length}</p>
                  </div>
                  <BarChart3 className="h-8 w-8 text-gray-400" aria-hidden="true" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-500">Learning Time</p>
                    <p className="text-2xl font-bold">{formatHours(learningPlan.totalEstimatedHours)}</p>
                  </div>
                  <Clock className="h-8 w-8 text-gray-400" aria-hidden="true" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-500">Learning Phases</p>
                    <p className="text-2xl font-bold">{learningPlan.phases.length}</p>
                  </div>
                  <BookOpen className="h-8 w-8 text-gray-400" aria-hidden="true" />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Skill Gaps Tab */}
        <TabsContent 
          value="gaps" 
          className="space-y-4"
          role="tabpanel"
          id="gaps-panel"
          aria-labelledby="gaps-tab"
          tabIndex={-1}
        >
          <h2 className="sr-only">Detailed Skill Gaps Analysis</h2>
          
          {skillGaps.length === 0 ? (
            <Card>
              <CardContent className="pt-6 text-center">
                <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" aria-hidden="true" />
                <h3 className="text-lg font-medium">No Skill Gaps Identified</h3>
                <p className="text-gray-500">Congratulations! Your current skills align well with your target career path.</p>
              </CardContent>
            </Card>
          ) : (
            skillGaps.map((gap, index) => (
              <Card 
                key={gap.skillId} 
                className="border-l-4" 
                style={{borderLeftColor: getSeverityColor(gap.gapSeverity).replace('bg-', '#')}}
                role="article"
                aria-labelledby={`gap-${gap.skillId}-title`}
              >
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle id={`gap-${gap.skillId}-title`} className="text-lg">
                      {gap.skillName}
                    </CardTitle>
                    <div className="flex items-center gap-2" role="group" aria-label="Skill gap indicators">
                      <Badge 
                        variant="secondary" 
                        className={`${getSeverityColor(gap.gapSeverity)} text-white`}
                        aria-label={getSeverityLabel(gap.gapSeverity)}
                      >
                        {gap.gapSeverity}
                      </Badge>
                      <Badge 
                        variant="outline" 
                        className={getDemandColor(gap.marketDemand)}
                        aria-label={`Market demand: ${gap.marketDemand.toLowerCase()}`}
                      >
                        {gap.marketDemand} Demand
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <div className="text-gray-500">Current Level</div>
                      <div className="font-medium" aria-label={`Current skill level: ${gap.currentLevel} out of 10`}>
                        {gap.currentLevel}/10
                      </div>
                    </div>
                    <div>
                      <div className="text-gray-500">Target Level</div>
                      <div className="font-medium" aria-label={`Target skill level: ${gap.targetLevel} out of 10`}>
                        {gap.targetLevel}/10
                      </div>
                    </div>
                    <div>
                      <div className="text-gray-500">Priority</div>
                      <div className="font-medium" aria-label={`Priority score: ${gap.priority} out of 100`}>
                        {gap.priority}/100
                      </div>
                    </div>
                    <div>
                      <div className="text-gray-500">Learning Time</div>
                      <div className="font-medium">{formatHours(gap.estimatedLearningTime)}</div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Progress to Target</span>
                      <span aria-label={`${Math.round((gap.currentLevel / gap.targetLevel) * 100)} percent complete`}>
                        {Math.round((gap.currentLevel / gap.targetLevel) * 100)}%
                      </span>
                    </div>
                    <Progress 
                      value={(gap.currentLevel / gap.targetLevel) * 100} 
                      className="h-2"
                      aria-label={`Skill progress for ${gap.skillName}: ${Math.round((gap.currentLevel / gap.targetLevel) * 100)}% complete`}
                    />
                  </div>

                  {gap.description && (
                    <p className="text-sm text-gray-600">{gap.description}</p>
                  )}
                </CardContent>
              </Card>
            ))
          )}
        </TabsContent>

        {/* Learning Plan Tab */}
        <TabsContent 
          value="plan" 
          className="space-y-6"
          role="tabpanel"
          id="plan-panel"
          aria-labelledby="plan-tab"
          tabIndex={-1}
        >
          <h2 className="sr-only">Personalized Learning Plan</h2>
          
          {/* Plan Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5" aria-hidden="true" />
                Learning Plan Overview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <div className="text-gray-500">Total Time Investment</div>
                  <div className="font-medium text-lg">{formatHours(learningPlan.totalEstimatedHours)}</div>
                </div>
                <div>
                  <div className="text-gray-500">Learning Phases</div>
                  <div className="font-medium text-lg">{learningPlan.phases.length}</div>
                </div>
                <div>
                  <div className="text-gray-500">Key Milestones</div>
                  <div className="font-medium text-lg">{learningPlan.milestones.length}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Learning Phases */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Learning Phases</h3>
            {learningPlan.phases.map((phase, index) => (
              <Card key={phase.phase} role="article" aria-labelledby={`phase-${phase.phase}-title`}>
                <CardHeader>
                  <CardTitle id={`phase-${phase.phase}-title`} className="flex items-center gap-2">
                    <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-medium">
                      {phase.phase}
                    </div>
                    {phase.title}
                  </CardTitle>
                  <CardDescription>
                    Duration: {formatHours(phase.duration)} | Skills: {phase.skills.length}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">Skills to Develop</h4>
                    <div className="flex flex-wrap gap-2" role="list" aria-label="Skills in this phase">
                      {phase.skills.map((skill, skillIndex) => (
                        <Badge key={skillIndex} variant="outline" role="listitem">
                          {skill}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">Milestones</h4>
                    <ul className="space-y-1 text-sm" role="list">
                      {phase.milestones.map((milestone, milestoneIndex) => (
                        <li key={milestoneIndex} role="listitem" className="flex items-start gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" aria-hidden="true" />
                          {milestone}
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Market Insights Tab */}
        <TabsContent 
          value="market" 
          className="space-y-6"
          role="tabpanel"
          id="market-panel"
          aria-labelledby="market-tab"
          tabIndex={-1}
        >
          <h2 className="sr-only">Market Insights and Salary Projections</h2>
          
          {marketInsights ? (
            <>
              {/* Salary Projections */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <DollarSign className="h-5 w-5" aria-hidden="true" />
                    Salary Projections
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-medium mb-2">Current Range</h4>
                      <div className="text-2xl font-bold">
                        {formatCurrency(marketInsights.salaryProjections.currentRange.min)} - {formatCurrency(marketInsights.salaryProjections.currentRange.max)}
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium mb-2">Target Range</h4>
                      <div className="text-2xl font-bold text-green-600">
                        {formatCurrency(marketInsights.salaryProjections.targetRange.min)} - {formatCurrency(marketInsights.salaryProjections.targetRange.max)}
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-4 bg-green-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-5 w-5 text-green-600" aria-hidden="true" />
                      <span className="font-medium text-green-800">
                        Potential Increase: {formatCurrency(marketInsights.salaryProjections.targetRange.min - marketInsights.salaryProjections.currentRange.max)}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Job Market Data */}
              <Card>
                <CardHeader>
                  <CardTitle>Job Market Overview</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <div className="text-gray-500">Open Positions</div>
                      <div className="text-2xl font-bold">{marketInsights.jobMarketData.openPositions.toLocaleString()}</div>
                    </div>
                    <div>
                      <div className="text-gray-500">Competition Level</div>
                      <div className="text-lg font-medium capitalize">{marketInsights.jobMarketData.competitionLevel}</div>
                    </div>
                    <div>
                      <div className="text-gray-500">Hiring Trends</div>
                      <div className="text-lg font-medium capitalize flex items-center gap-2">
                        {marketInsights.jobMarketData.hiringTrends === 'positive' ? (
                          <TrendingUp className="h-4 w-4 text-green-500" aria-hidden="true" />
                        ) : (
                          <TrendingDown className="h-4 w-4 text-red-500" aria-hidden="true" />
                        )}
                        {marketInsights.jobMarketData.hiringTrends}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </>
          ) : (
            <Card>
              <CardContent className="pt-6 text-center">
                <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" aria-hidden="true" />
                <h3 className="text-lg font-medium">Market Insights Unavailable</h3>
                <p className="text-gray-500">Market data is not available for this analysis.</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
