'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Target, 
  CheckCircle,
  AlertTriangle,
  Play,
  Pause,
  Square
} from 'lucide-react';
import { SKILL_GAP_AB_TESTS } from '@/lib/ab-testing/ab-test-framework';

interface ABTestResult {
  testId: string;
  variantId: string;
  participantCount: number;
  conversionRate: number;
  averageValue: number;
  confidenceInterval: {
    lower: number;
    upper: number;
    confidence: number;
  };
  statisticalSignificance: boolean;
  pValue: number;
}

interface TestResults {
  [testId: string]: ABTestResult[];
}

export default function ABTestDashboard() {
  const [testResults, setTestResults] = useState<TestResults>({});
  const [loading, setLoading] = useState(true);
  const [selectedTest, setSelectedTest] = useState<string>('');

  useEffect(() => {
    fetchTestResults();
  }, []);

  const fetchTestResults = async () => {
    setLoading(true);
    try {
      const activeTests = Object.keys(SKILL_GAP_AB_TESTS).filter(
        testId => SKILL_GAP_AB_TESTS[testId].status === 'active'
      );

      const resultsPromises = activeTests.map(async (testId) => {
        const response = await fetch(`/api/ab-testing/results/${testId}`);
        if (response.ok) {
          const data = await response.json();
          return { testId, results: data.results };
        }
        return { testId, results: [] };
      });

      const allResults = await Promise.all(resultsPromises);
      const resultsMap: TestResults = {};
      
      allResults.forEach(({ testId, results }) => {
        resultsMap[testId] = results;
      });

      setTestResults(resultsMap);
      
      if (activeTests.length > 0 && !selectedTest) {
        setSelectedTest(activeTests[0]);
      }
    } catch (error) {
      console.error('Failed to fetch A/B test results:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500';
      case 'paused': return 'bg-yellow-500';
      case 'completed': return 'bg-blue-500';
      case 'draft': return 'bg-gray-500';
      default: return 'bg-gray-500';
    }
  };

  const getSignificanceColor = (isSignificant: boolean, pValue: number) => {
    if (isSignificant) return 'text-green-600';
    if (pValue < 0.1) return 'text-yellow-600';
    return 'text-gray-600';
  };

  const calculateLift = (testRate: number, controlRate: number) => {
    if (controlRate === 0) return 0;
    return ((testRate - controlRate) / controlRate) * 100;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">A/B Testing Dashboard</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="pt-6">
                <div className="space-y-2">
                  <div className="h-4 w-20 bg-gray-200 rounded animate-pulse" />
                  <div className="h-8 w-16 bg-gray-200 rounded animate-pulse" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  const activeTests = Object.values(SKILL_GAP_AB_TESTS).filter(test => test.status === 'active');
  const totalParticipants = Object.values(testResults).flat().reduce(
    (sum, result) => sum + result.participantCount, 0
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">A/B Testing Dashboard</h2>
        <Button onClick={fetchTestResults} variant="outline">
          Refresh Data
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Active Tests</p>
                <p className="text-2xl font-bold">{activeTests.length}</p>
              </div>
              <Play className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Total Participants</p>
                <p className="text-2xl font-bold">{formatNumber(totalParticipants)}</p>
              </div>
              <Users className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Significant Results</p>
                <p className="text-2xl font-bold">
                  {Object.values(testResults).flat().filter(r => r.statisticalSignificance).length}
                </p>
              </div>
              <Target className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Avg Conversion</p>
                <p className="text-2xl font-bold">
                  {formatPercentage(
                    Object.values(testResults).flat().reduce(
                      (sum, result) => sum + result.conversionRate, 0
                    ) / Math.max(Object.values(testResults).flat().length, 1)
                  )}
                </p>
              </div>
              <BarChart3 className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Test Details */}
      <Tabs value={selectedTest} onValueChange={setSelectedTest}>
        <TabsList className="grid w-full grid-cols-3">
          {activeTests.slice(0, 3).map((test) => (
            <TabsTrigger key={test.testId} value={test.testId}>
              {test.name}
            </TabsTrigger>
          ))}
        </TabsList>

        {activeTests.map((test) => (
          <TabsContent key={test.testId} value={test.testId} className="space-y-6">
            {/* Test Overview */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>{test.name}</CardTitle>
                    <CardDescription>{test.description}</CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={`${getStatusColor(test.status)} text-white`}>
                      {test.status}
                    </Badge>
                    <Badge variant="outline">
                      {test.targetAudience.percentage}% traffic
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <p className="text-gray-500">Hypothesis</p>
                    <p className="font-medium">{test.hypothesis}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Success Criteria</p>
                    <p className="font-medium">{test.successCriteria}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Primary Metric</p>
                    <p className="font-medium">
                      {test.metrics.find(m => m.primaryMetric)?.name || 'N/A'}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Results */}
            {testResults[test.testId] && testResults[test.testId].length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {testResults[test.testId].map((result) => {
                  const isControl = result.variantId === 'control';
                  const controlResult = testResults[test.testId].find(r => r.variantId === 'control');
                  const lift = controlResult && !isControl 
                    ? calculateLift(result.conversionRate, controlResult.conversionRate)
                    : 0;

                  return (
                    <Card key={result.variantId} className={isControl ? 'border-blue-200' : ''}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-lg">
                            {test.variants.find(v => v.id === result.variantId)?.name || result.variantId}
                          </CardTitle>
                          <div className="flex items-center gap-2">
                            {isControl && <Badge variant="outline">Control</Badge>}
                            {result.statisticalSignificance && (
                              <Badge className="bg-green-500 text-white">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Significant
                              </Badge>
                            )}
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <p className="text-gray-500">Participants</p>
                            <p className="text-xl font-bold">{formatNumber(result.participantCount)}</p>
                          </div>
                          <div>
                            <p className="text-gray-500">Conversion Rate</p>
                            <p className="text-xl font-bold">{formatPercentage(result.conversionRate)}</p>
                          </div>
                        </div>

                        {!isControl && (
                          <div className="space-y-2">
                            <div className="flex items-center justify-between text-sm">
                              <span>Lift vs Control</span>
                              <span className={`font-medium flex items-center gap-1 ${
                                lift > 0 ? 'text-green-600' : lift < 0 ? 'text-red-600' : 'text-gray-600'
                              }`}>
                                {lift > 0 ? (
                                  <TrendingUp className="h-3 w-3" />
                                ) : lift < 0 ? (
                                  <TrendingDown className="h-3 w-3" />
                                ) : null}
                                {lift > 0 ? '+' : ''}{lift.toFixed(1)}%
                              </span>
                            </div>
                            
                            <div className="flex items-center justify-between text-sm">
                              <span>Statistical Significance</span>
                              <span className={getSignificanceColor(result.statisticalSignificance, result.pValue)}>
                                p = {result.pValue.toFixed(3)}
                              </span>
                            </div>
                          </div>
                        )}

                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Confidence Interval (95%)</span>
                            <span>
                              {formatPercentage(result.confidenceInterval.lower)} - {formatPercentage(result.confidenceInterval.upper)}
                            </span>
                          </div>
                          <Progress 
                            value={result.conversionRate * 100} 
                            className="h-2"
                          />
                        </div>

                        {result.averageValue > 0 && (
                          <div className="pt-2 border-t">
                            <div className="flex justify-between text-sm">
                              <span>Average Value</span>
                              <span className="font-medium">{result.averageValue.toFixed(2)}</span>
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            ) : (
              <Card>
                <CardContent className="pt-6 text-center">
                  <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium">No Results Yet</h3>
                  <p className="text-gray-500">
                    This test is running but doesn't have enough data to show results.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        ))}
      </Tabs>

      {/* Test Configuration */}
      {selectedTest && (
        <Card>
          <CardHeader>
            <CardTitle>Test Configuration</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Variants</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {SKILL_GAP_AB_TESTS[selectedTest].variants.map((variant) => (
                    <div key={variant.id} className="p-3 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h5 className="font-medium">{variant.name}</h5>
                        <Badge variant="outline">{variant.trafficAllocation}%</Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{variant.description}</p>
                      <div className="text-xs text-gray-500">
                        Config: {JSON.stringify(variant.config, null, 2)}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2">Metrics</h4>
                <div className="space-y-2">
                  {SKILL_GAP_AB_TESTS[selectedTest].metrics.map((metric) => (
                    <div key={metric.name} className="flex items-center justify-between p-2 border rounded">
                      <div>
                        <span className="font-medium">{metric.name}</span>
                        {metric.primaryMetric && (
                          <Badge variant="outline" className="ml-2">Primary</Badge>
                        )}
                      </div>
                      <div className="text-sm text-gray-500">
                        {metric.type} • {metric.targetDirection}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
