'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';

interface AccessibilitySettings {
  reducedMotion: boolean;
  highContrast: boolean;
  largeText: boolean;
  screenReaderMode: boolean;
  keyboardNavigation: boolean;
  focusVisible: boolean;
}

interface AccessibilityContextType {
  settings: AccessibilitySettings;
  updateSetting: (key: keyof AccessibilitySettings, value: boolean) => void;
  announceToScreenReader: (message: string, priority?: 'polite' | 'assertive') => void;
  setFocusToElement: (elementId: string) => void;
  skipToContent: () => void;
}

const AccessibilityContext = createContext<AccessibilityContextType | undefined>(undefined);

export function useAccessibility() {
  const context = useContext(AccessibilityContext);
  if (!context) {
    throw new Error('useAccessibility must be used within an AccessibilityProvider');
  }
  return context;
}

interface AccessibilityProviderProps {
  children: React.ReactNode;
}

export function AccessibilityProvider({ children }: AccessibilityProviderProps) {
  const [settings, setSettings] = useState<AccessibilitySettings>({
    reducedMotion: false,
    highContrast: false,
    largeText: false,
    screenReaderMode: false,
    keyboardNavigation: true,
    focusVisible: true,
  });

  // Detect user preferences on mount
  useEffect(() => {
    const detectPreferences = () => {
      const reducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
      const highContrast = window.matchMedia('(prefers-contrast: high)').matches;
      const largeText = window.matchMedia('(prefers-font-size: large)').matches;
      
      setSettings(prev => ({
        ...prev,
        reducedMotion,
        highContrast,
        largeText,
      }));
    };

    detectPreferences();

    // Listen for changes in user preferences
    const mediaQueries = [
      window.matchMedia('(prefers-reduced-motion: reduce)'),
      window.matchMedia('(prefers-contrast: high)'),
      window.matchMedia('(prefers-font-size: large)'),
    ];

    mediaQueries.forEach(mq => {
      mq.addEventListener('change', detectPreferences);
    });

    return () => {
      mediaQueries.forEach(mq => {
        mq.removeEventListener('change', detectPreferences);
      });
    };
  }, []);

  // Apply accessibility settings to document
  useEffect(() => {
    const root = document.documentElement;
    
    // Apply CSS custom properties based on settings
    root.style.setProperty('--motion-duration', settings.reducedMotion ? '0ms' : '200ms');
    root.style.setProperty('--motion-scale', settings.reducedMotion ? '1' : '1.02');
    
    if (settings.highContrast) {
      root.classList.add('high-contrast');
    } else {
      root.classList.remove('high-contrast');
    }
    
    if (settings.largeText) {
      root.classList.add('large-text');
    } else {
      root.classList.remove('large-text');
    }
    
    if (settings.screenReaderMode) {
      root.classList.add('screen-reader-mode');
    } else {
      root.classList.remove('screen-reader-mode');
    }
    
    if (settings.focusVisible) {
      root.classList.add('focus-visible-enabled');
    } else {
      root.classList.remove('focus-visible-enabled');
    }
  }, [settings]);

  const updateSetting = (key: keyof AccessibilitySettings, value: boolean) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    
    // Save to localStorage
    localStorage.setItem('accessibility-settings', JSON.stringify({
      ...settings,
      [key]: value,
    }));
  };

  const announceToScreenReader = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', priority);
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    // Remove after announcement
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  };

  const setFocusToElement = (elementId: string) => {
    const element = document.getElementById(elementId);
    if (element) {
      element.focus();
      announceToScreenReader(`Focus moved to ${element.getAttribute('aria-label') || element.textContent || elementId}`);
    }
  };

  const skipToContent = () => {
    const mainContent = document.getElementById('main-content') || document.querySelector('main');
    if (mainContent) {
      mainContent.focus();
      announceToScreenReader('Skipped to main content');
    }
  };

  const value: AccessibilityContextType = {
    settings,
    updateSetting,
    announceToScreenReader,
    setFocusToElement,
    skipToContent,
  };

  return (
    <AccessibilityContext.Provider value={value}>
      {children}
      <ScreenReaderAnnouncements />
      <SkipLinks />
    </AccessibilityContext.Provider>
  );
}

// Screen reader announcements component
function ScreenReaderAnnouncements() {
  return (
    <div
      id="screen-reader-announcements"
      aria-live="polite"
      aria-atomic="true"
      className="sr-only"
    />
  );
}

// Skip links component
function SkipLinks() {
  const { skipToContent } = useAccessibility();

  return (
    <div className="skip-links">
      <button
        className="skip-link"
        onClick={skipToContent}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            skipToContent();
          }
        }}
      >
        Skip to main content
      </button>
      <style jsx>{`
        .skip-links {
          position: absolute;
          top: -40px;
          left: 6px;
          z-index: 1000;
        }
        
        .skip-link {
          position: absolute;
          top: -40px;
          left: 6px;
          background: #000;
          color: #fff;
          padding: 8px;
          text-decoration: none;
          border: none;
          border-radius: 4px;
          font-size: 14px;
          z-index: 1000;
          transition: top 0.3s;
        }
        
        .skip-link:focus {
          top: 6px;
        }
      `}</style>
    </div>
  );
}

// Accessibility settings panel component
export function AccessibilitySettingsPanel() {
  const { settings, updateSetting } = useAccessibility();
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="accessibility-settings">
      <button
        className="accessibility-toggle"
        onClick={() => setIsOpen(!isOpen)}
        aria-expanded={isOpen}
        aria-controls="accessibility-panel"
        aria-label="Accessibility settings"
      >
        <span className="sr-only">Accessibility settings</span>
        ♿
      </button>
      
      {isOpen && (
        <div
          id="accessibility-panel"
          className="accessibility-panel"
          role="dialog"
          aria-labelledby="accessibility-panel-title"
        >
          <h3 id="accessibility-panel-title">Accessibility Settings</h3>
          
          <div className="setting-group">
            <label>
              <input
                type="checkbox"
                checked={settings.reducedMotion}
                onChange={(e) => updateSetting('reducedMotion', e.target.checked)}
              />
              Reduce motion
            </label>
          </div>
          
          <div className="setting-group">
            <label>
              <input
                type="checkbox"
                checked={settings.highContrast}
                onChange={(e) => updateSetting('highContrast', e.target.checked)}
              />
              High contrast
            </label>
          </div>
          
          <div className="setting-group">
            <label>
              <input
                type="checkbox"
                checked={settings.largeText}
                onChange={(e) => updateSetting('largeText', e.target.checked)}
              />
              Large text
            </label>
          </div>
          
          <div className="setting-group">
            <label>
              <input
                type="checkbox"
                checked={settings.screenReaderMode}
                onChange={(e) => updateSetting('screenReaderMode', e.target.checked)}
              />
              Screen reader optimizations
            </label>
          </div>
          
          <button
            className="close-button"
            onClick={() => setIsOpen(false)}
            aria-label="Close accessibility settings"
          >
            Close
          </button>
        </div>
      )}
      
      <style jsx>{`
        .accessibility-settings {
          position: fixed;
          top: 20px;
          right: 20px;
          z-index: 1000;
        }
        
        .accessibility-toggle {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          background: #007acc;
          color: white;
          border: none;
          font-size: 24px;
          cursor: pointer;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }
        
        .accessibility-toggle:focus {
          outline: 3px solid #ffbf00;
          outline-offset: 2px;
        }
        
        .accessibility-panel {
          position: absolute;
          top: 60px;
          right: 0;
          width: 280px;
          background: white;
          border: 1px solid #ccc;
          border-radius: 8px;
          padding: 16px;
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
        }
        
        .setting-group {
          margin-bottom: 12px;
        }
        
        .setting-group label {
          display: flex;
          align-items: center;
          gap: 8px;
          cursor: pointer;
        }
        
        .close-button {
          width: 100%;
          padding: 8px;
          background: #007acc;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          margin-top: 16px;
        }
        
        .close-button:focus {
          outline: 3px solid #ffbf00;
          outline-offset: 2px;
        }
      `}</style>
    </div>
  );
}
