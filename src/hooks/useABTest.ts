/**
 * React Hook for A/B Testing Integration
 * Provides easy-to-use A/B testing functionality for React components
 */

import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { abTestFramework } from '@/lib/ab-testing/ab-test-framework';

interface UseABTestOptions {
  testId: string;
  userSegments?: string[];
  fallbackVariant?: string;
  onVariantAssigned?: (variant: string) => void;
  onConversion?: (metricName: string, value?: number) => void;
}

interface ABTestHook {
  variant: string | null;
  config: Record<string, any> | null;
  isLoading: boolean;
  isInTest: boolean;
  trackConversion: (metricName: string, value?: number) => Promise<void>;
  isVariant: (variantId: string) => boolean;
  getConfigValue: <T>(key: string, defaultValue?: T) => T;
}

export function useABTest(options: UseABTestOptions): ABTestHook {
  const { data: session } = useSession();
  const [variant, setVariant] = useState<string | null>(null);
  const [config, setConfig] = useState<Record<string, any> | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const {
    testId,
    userSegments = [],
    fallbackVariant = 'control',
    onVariantAssigned,
    onConversion
  } = options;

  // Get variant assignment
  useEffect(() => {
    async function assignVariant() {
      if (!session?.user?.id) {
        setIsLoading(false);
        return;
      }

      try {
        const assignedVariant = await abTestFramework.getVariant(
          testId,
          session.user.id,
          userSegments
        );

        const finalVariant = assignedVariant || fallbackVariant;
        setVariant(finalVariant);

        // Get variant configuration
        const variantConfig = abTestFramework.getVariantConfig(testId, finalVariant);
        setConfig(variantConfig);

        // Notify parent component
        if (onVariantAssigned && assignedVariant) {
          onVariantAssigned(assignedVariant);
        }
      } catch (error) {
        console.error('Failed to assign A/B test variant:', error);
        setVariant(fallbackVariant);
        setConfig(null);
      } finally {
        setIsLoading(false);
      }
    }

    assignVariant();
  }, [session?.user?.id, testId, userSegments, fallbackVariant, onVariantAssigned]);

  // Track conversion
  const trackConversion = useCallback(async (metricName: string, value?: number) => {
    if (!session?.user?.id || !variant) {
      return;
    }

    try {
      await abTestFramework.trackConversion(testId, session.user.id, metricName, value);
      
      // Notify parent component
      if (onConversion) {
        onConversion(metricName, value);
      }
    } catch (error) {
      console.error('Failed to track A/B test conversion:', error);
    }
  }, [session?.user?.id, testId, variant, onConversion]);

  // Check if current variant matches
  const isVariant = useCallback((variantId: string) => {
    return variant === variantId;
  }, [variant]);

  // Get configuration value
  const getConfigValue = useCallback(<T>(key: string, defaultValue?: T): T => {
    if (!config || !(key in config)) {
      return defaultValue as T;
    }
    return config[key] as T;
  }, [config]);

  return {
    variant,
    config,
    isLoading,
    isInTest: variant !== null && variant !== fallbackVariant,
    trackConversion,
    isVariant,
    getConfigValue
  };
}

/**
 * Hook for multiple A/B tests
 */
interface UseMultipleABTestsOptions {
  tests: Array<{
    testId: string;
    userSegments?: string[];
    fallbackVariant?: string;
  }>;
}

interface MultipleABTestsHook {
  variants: Record<string, string | null>;
  configs: Record<string, Record<string, any> | null>;
  isLoading: boolean;
  trackConversion: (testId: string, metricName: string, value?: number) => Promise<void>;
  isVariant: (testId: string, variantId: string) => boolean;
  getConfigValue: <T>(testId: string, key: string, defaultValue?: T) => T;
}

export function useMultipleABTests(options: UseMultipleABTestsOptions): MultipleABTestsHook {
  const { data: session } = useSession();
  const [variants, setVariants] = useState<Record<string, string | null>>({});
  const [configs, setConfigs] = useState<Record<string, Record<string, any> | null>>({});
  const [isLoading, setIsLoading] = useState(true);

  const { tests } = options;

  // Get variant assignments for all tests
  useEffect(() => {
    async function assignVariants() {
      if (!session?.user?.id) {
        setIsLoading(false);
        return;
      }

      try {
        const variantPromises = tests.map(async (test) => {
          const assignedVariant = await abTestFramework.getVariant(
            test.testId,
            session.user.id,
            test.userSegments || []
          );

          const finalVariant = assignedVariant || test.fallbackVariant || 'control';
          const variantConfig = abTestFramework.getVariantConfig(test.testId, finalVariant);

          return {
            testId: test.testId,
            variant: finalVariant,
            config: variantConfig
          };
        });

        const results = await Promise.all(variantPromises);

        const newVariants: Record<string, string | null> = {};
        const newConfigs: Record<string, Record<string, any> | null> = {};

        results.forEach(({ testId, variant, config }) => {
          newVariants[testId] = variant;
          newConfigs[testId] = config;
        });

        setVariants(newVariants);
        setConfigs(newConfigs);
      } catch (error) {
        console.error('Failed to assign A/B test variants:', error);
        
        // Set fallback variants
        const fallbackVariants: Record<string, string | null> = {};
        const fallbackConfigs: Record<string, Record<string, any> | null> = {};
        
        tests.forEach(test => {
          fallbackVariants[test.testId] = test.fallbackVariant || 'control';
          fallbackConfigs[test.testId] = null;
        });
        
        setVariants(fallbackVariants);
        setConfigs(fallbackConfigs);
      } finally {
        setIsLoading(false);
      }
    }

    assignVariants();
  }, [session?.user?.id, tests]);

  // Track conversion for specific test
  const trackConversion = useCallback(async (testId: string, metricName: string, value?: number) => {
    if (!session?.user?.id || !variants[testId]) {
      return;
    }

    try {
      await abTestFramework.trackConversion(testId, session.user.id, metricName, value);
    } catch (error) {
      console.error('Failed to track A/B test conversion:', error);
    }
  }, [session?.user?.id, variants]);

  // Check if current variant matches for specific test
  const isVariant = useCallback((testId: string, variantId: string) => {
    return variants[testId] === variantId;
  }, [variants]);

  // Get configuration value for specific test
  const getConfigValue = useCallback(<T>(testId: string, key: string, defaultValue?: T): T => {
    const config = configs[testId];
    if (!config || !(key in config)) {
      return defaultValue as T;
    }
    return config[key] as T;
  }, [configs]);

  return {
    variants,
    configs,
    isLoading,
    trackConversion,
    isVariant,
    getConfigValue
  };
}

/**
 * Hook for feature flags (simplified A/B testing)
 */
interface UseFeatureFlagOptions {
  flagName: string;
  defaultValue?: boolean;
  userSegments?: string[];
}

interface FeatureFlagHook {
  isEnabled: boolean;
  isLoading: boolean;
  trackUsage: () => Promise<void>;
}

export function useFeatureFlag(options: UseFeatureFlagOptions): FeatureFlagHook {
  const { flagName, defaultValue = false, userSegments = [] } = options;
  
  const abTest = useABTest({
    testId: `feature-flag-${flagName}`,
    userSegments,
    fallbackVariant: defaultValue ? 'enabled' : 'disabled'
  });

  const isEnabled = abTest.variant === 'enabled';

  const trackUsage = useCallback(async () => {
    if (isEnabled) {
      await abTest.trackConversion('feature_used');
    }
  }, [isEnabled, abTest]);

  return {
    isEnabled,
    isLoading: abTest.isLoading,
    trackUsage
  };
}

/**
 * Higher-order component for A/B testing
 */
interface WithABTestProps {
  testId: string;
  variants: Record<string, React.ComponentType<any>>;
  fallbackVariant?: string;
  userSegments?: string[];
  loadingComponent?: React.ComponentType;
}

export function withABTest<P extends object>(
  options: WithABTestProps
): React.ComponentType<P> {
  const {
    testId,
    variants,
    fallbackVariant = 'control',
    userSegments = [],
    loadingComponent: LoadingComponent
  } = options;

  return function ABTestWrapper(props: P) {
    const abTest = useABTest({
      testId,
      userSegments,
      fallbackVariant
    });

    if (abTest.isLoading) {
      return LoadingComponent ? <LoadingComponent /> : null;
    }

    const VariantComponent = variants[abTest.variant || fallbackVariant];
    
    if (!VariantComponent) {
      console.warn(`No component found for variant: ${abTest.variant}`);
      const FallbackComponent = variants[fallbackVariant];
      return FallbackComponent ? <FallbackComponent {...props} /> : null;
    }

    return <VariantComponent {...props} abTest={abTest} />;
  };
}
