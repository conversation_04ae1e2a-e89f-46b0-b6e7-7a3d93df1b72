<!DOCTYPE html>
<html>
<head>
    <title>Clear Authentication Data</title>
</head>
<body>
    <h1>Clearing Authentication Data...</h1>
    <script>
        // Clear all localStorage
        localStorage.clear();
        
        // Clear all sessionStorage
        sessionStorage.clear();
        
        // Clear all cookies
        document.cookie.split(";").forEach(function(c) { 
            document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
        });
        
        // Clear IndexedDB
        if ('indexedDB' in window) {
            indexedDB.databases().then(databases => {
                databases.forEach(db => {
                    indexedDB.deleteDatabase(db.name);
                });
            });
        }
        
        console.log('All authentication data cleared');
        document.body.innerHTML += '<p>Authentication data cleared. You can close this tab.</p>';
    </script>
</body>
</html>
