#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function debugAuthSystem() {
  console.log('🔍 Debugging Authentication System...\n');

  try {
    // 1. Check database connection
    console.log('1️⃣ Testing database connection...');
    await prisma.$connect();
    console.log('✅ Database connection successful\n');

    // 2. Check for test users (<EMAIL> has been eliminated)
    console.log('2️⃣ Checking for test users...');
    const testUsers = await prisma.user.findMany({
      where: {
        email: {
          contains: 'test'
        }
      },
      include: {
        accounts: true,
        sessions: true,
      }
    });

    if (testUsers.length > 0) {
      console.log(`✅ Found ${testUsers.length} test users:`);
      testUsers.forEach(user => {
        console.log(`   Email: ${user.email}`);
        console.log(`   ID: ${user.id}`);
        console.log(`   Name: ${user.name}`);
        console.log(`   Email Verified: ${user.emailVerified}`);
        console.log(`   Failed Login Attempts: ${user.failedLoginAttempts}`);
        console.log(`   Locked Until: ${user.lockedUntil}`);
        console.log(`   Accounts: ${user.accounts.length}`);
        console.log(`   Sessions: ${user.sessions.length}`);
        console.log('   ---');
      });
    } else {
      console.log('ℹ️ No test users found - <EMAIL> has been eliminated');
      console.log('ℹ️ Use proper user registration flow for testing');
    }

    // 4. Check for constraint violations
    console.log('\n4️⃣ Checking for constraint violations...');
    
    // Check for duplicate emails
    const duplicateEmails = await prisma.$queryRaw`
      SELECT email, COUNT(*) as count 
      FROM "User" 
      GROUP BY email 
      HAVING COUNT(*) > 1
    `;
    
    if (Array.isArray(duplicateEmails) && duplicateEmails.length > 0) {
      console.log('❌ Found duplicate emails:');
      duplicateEmails.forEach((row: any) => {
        console.log(`   ${row.email}: ${row.count} occurrences`);
      });
    } else {
      console.log('✅ No duplicate emails found');
    }

    // Check for orphaned sessions (sessions with invalid user IDs)
    const allSessions = await prisma.session.findMany({
      include: { user: true }
    });

    const orphanedSessions = allSessions.filter(session => !session.user);

    if (orphanedSessions.length > 0) {
      console.log(`❌ Found ${orphanedSessions.length} orphaned sessions`);
    } else {
      console.log('✅ No orphaned sessions found');
    }

    // Check for orphaned accounts (accounts with invalid user IDs)
    const allAccounts = await prisma.account.findMany({
      include: { user: true }
    });

    const orphanedAccounts = allAccounts.filter(account => !account.user);

    if (orphanedAccounts.length > 0) {
      console.log(`❌ Found ${orphanedAccounts.length} orphaned accounts`);
    } else {
      console.log('✅ No orphaned accounts found');
    }

    // 5. Check ID format consistency
    console.log('\n5️⃣ Checking ID format consistency...');
    
    const users = await prisma.user.findMany({
      select: { id: true, email: true }
    });
    
    const invalidIds = users.filter(user => !user.id.match(/^[a-z0-9]+$/));
    
    if (invalidIds.length > 0) {
      console.log('❌ Found users with invalid ID format:');
      invalidIds.forEach(user => {
        console.log(`   ${user.email}: ${user.id}`);
      });
    } else {
      console.log('✅ All user IDs have valid format');
    }

    // 6. Test session creation (if test users exist)
    console.log('\n6️⃣ Testing session creation...');

    if (testUsers.length > 0) {
      const testUserForSession = testUsers[0]; // Use first available test user

      try {
        const testSession = await prisma.session.create({
          data: {
            sessionToken: `test-session-${Date.now()}`,
            userId: testUserForSession.id,
            expires: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
          }
        });

        console.log('✅ Session creation successful');
        console.log(`   Session ID: ${testSession.id}`);
        console.log(`   Session Token: ${testSession.sessionToken}`);

        // Clean up test session
        await prisma.session.delete({
          where: { id: testSession.id }
        });
        console.log('✅ Test session cleaned up');

      } catch (error) {
        console.log('❌ Session creation failed:');
        console.log(`   Error: ${error}`);
      }
    } else {
      console.log('ℹ️ Skipping session creation test - no test users available');
    }

    // 7. Environment variables check
    console.log('\n7️⃣ Checking environment variables...');
    
    const requiredEnvVars = [
      'DATABASE_URL',
      'NEXTAUTH_SECRET',
      'NEXTAUTH_URL'
    ];
    
    const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    if (missingEnvVars.length > 0) {
      console.log('❌ Missing environment variables:');
      missingEnvVars.forEach(varName => {
        console.log(`   ${varName}`);
      });
    } else {
      console.log('✅ All required environment variables present');
    }

    console.log('\n🎉 Authentication system debug completed!');

  } catch (error) {
    console.error('❌ Debug failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  debugAuthSystem();
}

export { debugAuthSystem };
