import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testUIData() {
  console.log('🔍 TESTING UI DATA AVAILABILITY');
  console.log('=' .repeat(50));

  try {
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: {
        skillAssessments: {
          include: {
            skill: true
          },
          orderBy: { createdAt: 'desc' }
        },
        skillGapAnalyses: {
          orderBy: { createdAt: 'desc' },
          take: 1
        }
      }
    });

    if (!user) {
      throw new Error('User not found');
    }

    console.log('\n📊 SKILL ASSESSMENTS FOR UI:');
    console.log(`Total: ${user.skillAssessments.length}`);
    user.skillAssessments.forEach((assessment, index) => {
      console.log(`${index + 1}. ${assessment.skill.name}`);
      console.log(`   Self Rating: ${assessment.selfRating}/10`);
      console.log(`   Confidence: ${assessment.confidenceLevel}/10`);
      console.log(`   Notes: ${assessment.notes || 'None'}`);
      console.log(`   Date: ${assessment.assessmentDate.toISOString().split('T')[0]}`);
      console.log('');
    });

    console.log('\n🎯 GAP ANALYSIS FOR UI:');
    if (user.skillGapAnalyses.length > 0) {
      const analysis = user.skillGapAnalyses[0];
      console.log(`Analysis ID: ${analysis.id}`);
      console.log(`Target Career: ${analysis.targetCareerPathName}`);
      console.log(`Experience Level: ${analysis.experienceLevel}`);
      console.log(`Timeframe: ${analysis.timeframe}`);
      console.log(`Status: ${analysis.status}`);
      console.log(`Completion: ${analysis.completionPercentage}%`);
      
      // Parse and display skill gaps
      const skillGaps = analysis.skillGaps as any[];
      console.log(`\nSkill Gaps (${skillGaps.length}):`);
      skillGaps.forEach((gap, index) => {
        console.log(`${index + 1}. ${gap.skillName}: ${gap.currentLevel} → ${gap.targetLevel}`);
        console.log(`   Gap Size: ${gap.gapSize} (${gap.gapSeverity} priority)`);
        console.log(`   Learning Time: ${gap.estimatedLearningTime} hours`);
      });

      // Parse and display learning plan
      const learningPlan = analysis.learningPlan as any;
      console.log(`\nLearning Plan:`);
      console.log(`Total Hours: ${learningPlan.totalEstimatedHours}`);
      console.log(`Weekly Commitment: ${learningPlan.weeklyCommitment} hours`);
      console.log(`Milestones: ${learningPlan.milestones?.length || 0}`);

      // Parse and display market data
      const marketData = analysis.marketData as any;
      if (marketData?.careerReadiness) {
        console.log(`\nCareer Readiness:`);
        console.log(`Current Score: ${marketData.careerReadiness.currentScore}/100`);
        console.log(`Target Score: ${marketData.careerReadiness.targetScore}/100`);
        console.log(`Improvement Potential: ${marketData.careerReadiness.improvementPotential} points`);
      }

      console.log('\n✅ ALL DATA AVAILABLE FOR UI DISPLAY');
    } else {
      console.log('❌ No gap analysis found');
    }

    // Test what the UI components would receive
    console.log('\n🖥️  UI COMPONENT DATA STRUCTURE:');
    console.log('Assessment Form would show:');
    console.log(`- ${user.skillAssessments.length} existing assessments`);
    console.log('- Skill search functionality');
    console.log('- Assessment submission form');

    console.log('\nGap Analysis would show:');
    if (user.skillGapAnalyses.length > 0) {
      console.log('- Overview tab with career readiness score');
      console.log('- Gaps tab with skill gap details');
      console.log('- Learning plan tab with milestones');
      console.log('- Market insights tab with salary data');
    }

  } catch (error) {
    console.error('❌ Error testing UI data:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testUIData();
