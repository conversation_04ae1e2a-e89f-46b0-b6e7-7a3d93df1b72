import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function eliminateTestUser() {
  const testEmail = '<EMAIL>';
  
  try {
    console.log('🗑️ Eliminating test user:', testEmail);
    
    // Find the user first
    const user = await prisma.user.findUnique({
      where: { email: testEmail },
      include: {
        skillAssessments: true,
        skillGapAnalyses: true,
        assessments: true,
        freedomFund: true,
        profile: true,
        accounts: true,
        sessions: true,
      }
    });
    
    if (!user) {
      console.log('✅ User not found in database - already eliminated');
      return;
    }
    
    console.log(`📊 Found user with ID: ${user.id}`);
    console.log(`   - Skill Assessments: ${user.skillAssessments.length}`);
    console.log(`   - Skill Gap Analyses: ${user.skillGapAnalyses.length}`);
    console.log(`   - Assessments: ${user.assessments.length}`);
    console.log(`   - Freedom Fund records: ${user.freedomFund.length}`);
    console.log(`   - Profile: ${user.profile ? 'Yes' : 'No'}`);
    console.log(`   - Accounts: ${user.accounts.length}`);
    console.log(`   - Sessions: ${user.sessions.length}`);
    
    // Delete all related data in the correct order (respecting foreign key constraints)
    console.log('\n🧹 Cleaning up related data...');
    
    // Delete skill assessments
    if (user.skillAssessments.length > 0) {
      await prisma.skillAssessment.deleteMany({
        where: { userId: user.id }
      });
      console.log(`   ✅ Deleted ${user.skillAssessments.length} skill assessments`);
    }
    
    // Delete skill gap analyses
    if (user.skillGapAnalyses.length > 0) {
      await prisma.skillGapAnalysis.deleteMany({
        where: { userId: user.id }
      });
      console.log(`   ✅ Deleted ${user.skillGapAnalyses.length} skill gap analyses`);
    }
    
    // Delete assessment responses first, then assessments
    if (user.assessments.length > 0) {
      for (const assessment of user.assessments) {
        await prisma.assessmentResponse.deleteMany({
          where: { assessmentId: assessment.id }
        });
      }
      await prisma.assessment.deleteMany({
        where: { userId: user.id }
      });
      console.log(`   ✅ Deleted ${user.assessments.length} assessments and their responses`);
    }
    
    // Delete freedom fund records
    if (user.freedomFund.length > 0) {
      await prisma.freedomFund.deleteMany({
        where: { userId: user.id }
      });
      console.log(`   ✅ Deleted ${user.freedomFund.length} freedom fund records`);
    }
    
    // Delete profile
    if (user.profile) {
      await prisma.userProfile.delete({
        where: { userId: user.id }
      });
      console.log(`   ✅ Deleted user profile`);
    }
    
    // Delete sessions
    if (user.sessions.length > 0) {
      await prisma.session.deleteMany({
        where: { userId: user.id }
      });
      console.log(`   ✅ Deleted ${user.sessions.length} sessions`);
    }
    
    // Delete accounts
    if (user.accounts.length > 0) {
      await prisma.account.deleteMany({
        where: { userId: user.id }
      });
      console.log(`   ✅ Deleted ${user.accounts.length} accounts`);
    }
    
    // Delete verification tokens
    await prisma.verificationToken.deleteMany({
      where: { identifier: testEmail }
    });
    console.log(`   ✅ Deleted verification tokens`);
    
    // Finally, delete the user
    await prisma.user.delete({
      where: { id: user.id }
    });
    
    console.log('\n🎉 Test user completely eliminated from database');
    
  } catch (error) {
    console.error('❌ Error eliminating test user:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the elimination
eliminateTestUser()
  .then(() => {
    console.log('✅ Elimination complete');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Elimination failed:', error);
    process.exit(1);
  });
