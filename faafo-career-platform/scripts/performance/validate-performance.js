#!/usr/bin/env node

/**
 * Performance Validation Script
 * Validates that the Skill Gap Analyzer meets performance requirements
 */

const SkillGapLoadTester = require('./load-test-skill-gap');
const PerformanceMonitor = require('./performance-monitor');
const fs = require('fs');
const path = require('path');

class PerformanceValidator {
  constructor(config = {}) {
    this.baseUrl = config.baseUrl || 'http://localhost:3000';
    this.requirements = {
      skillSearchResponseTime: 500, // ms
      skillAssessmentResponseTime: 1000, // ms
      comprehensiveAnalysisResponseTime: 10000, // ms
      maxErrorRate: 5, // %
      minThroughput: 1, // req/s
      maxMemoryIncrease: 50, // %
      ...config.requirements
    };
    
    this.results = {
      passed: 0,
      failed: 0,
      tests: [],
      summary: {},
    };
  }

  /**
   * Run all performance validation tests
   */
  async validate() {
    console.log('🚀 Starting Performance Validation for Skill Gap Analyzer');
    console.log(`Base URL: ${this.baseUrl}`);
    console.log('Requirements:', this.requirements);
    console.log('');

    try {
      // Test 1: Individual endpoint performance
      await this.validateEndpointPerformance();
      
      // Test 2: Load testing
      await this.validateLoadPerformance();
      
      // Test 3: Memory usage
      await this.validateMemoryUsage();
      
      // Test 4: Error handling performance
      await this.validateErrorHandling();
      
      // Test 5: Concurrent user performance
      await this.validateConcurrentUsers();

      // Generate final report
      this.generateFinalReport();
      
      return this.results;
    } catch (error) {
      console.error('❌ Performance validation failed:', error);
      throw error;
    }
  }

  /**
   * Validate individual endpoint performance
   */
  async validateEndpointPerformance() {
    console.log('📊 Testing Individual Endpoint Performance...');
    
    const loadTester = new SkillGapLoadTester({ baseUrl: this.baseUrl });
    const testData = loadTester.generateTestData();

    // Test skill search
    const searchStart = Date.now();
    const searchResult = await loadTester.testSkillSearch('JavaScript');
    const searchTime = Date.now() - searchStart;
    
    this.recordTest('Skill Search Response Time', 
      searchTime < this.requirements.skillSearchResponseTime,
      `${searchTime}ms (requirement: <${this.requirements.skillSearchResponseTime}ms)`
    );

    // Test skill assessment
    const assessmentStart = Date.now();
    const assessmentResult = await loadTester.testSkillAssessment(
      testData.assessments.slice(0, 3), 
      'validation-user'
    );
    const assessmentTime = Date.now() - assessmentStart;
    
    this.recordTest('Skill Assessment Response Time',
      assessmentTime < this.requirements.skillAssessmentResponseTime,
      `${assessmentTime}ms (requirement: <${this.requirements.skillAssessmentResponseTime}ms)`
    );

    // Test comprehensive analysis
    const analysisStart = Date.now();
    const analysisResult = await loadTester.testComprehensiveAnalysis(testData, 'validation-user');
    const analysisTime = Date.now() - analysisStart;
    
    this.recordTest('Comprehensive Analysis Response Time',
      analysisTime < this.requirements.comprehensiveAnalysisResponseTime,
      `${analysisTime}ms (requirement: <${this.requirements.comprehensiveAnalysisResponseTime}ms)`
    );

    console.log('✅ Individual endpoint performance tests completed\n');
  }

  /**
   * Validate load performance
   */
  async validateLoadPerformance() {
    console.log('🔄 Testing Load Performance...');
    
    const loadTester = new SkillGapLoadTester({
      baseUrl: this.baseUrl,
      concurrentUsers: 5,
      testDuration: 30000, // 30 seconds
      rampUpTime: 5000,
    });

    const loadResults = await loadTester.runLoadTest();
    
    this.recordTest('Error Rate Under Load',
      loadResults.errorRate < this.requirements.maxErrorRate,
      `${loadResults.errorRate.toFixed(2)}% (requirement: <${this.requirements.maxErrorRate}%)`
    );
    
    this.recordTest('Throughput Under Load',
      loadResults.throughput > this.requirements.minThroughput,
      `${loadResults.throughput.toFixed(2)} req/s (requirement: >${this.requirements.minThroughput} req/s)`
    );
    
    this.recordTest('Average Response Time Under Load',
      loadResults.averageResponseTime < 2000,
      `${loadResults.averageResponseTime.toFixed(2)}ms (requirement: <2000ms)`
    );

    console.log('✅ Load performance tests completed\n');
  }

  /**
   * Validate memory usage
   */
  async validateMemoryUsage() {
    console.log('💾 Testing Memory Usage...');
    
    const initialMemory = process.memoryUsage();
    const loadTester = new SkillGapLoadTester({
      baseUrl: this.baseUrl,
      concurrentUsers: 3,
      testDuration: 15000,
      rampUpTime: 3000,
    });

    await loadTester.runLoadTest();
    
    const finalMemory = process.memoryUsage();
    const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
    const memoryIncreasePercent = (memoryIncrease / initialMemory.heapUsed) * 100;
    
    this.recordTest('Memory Usage Increase',
      memoryIncreasePercent < this.requirements.maxMemoryIncrease,
      `${memoryIncreasePercent.toFixed(2)}% (requirement: <${this.requirements.maxMemoryIncrease}%)`
    );

    console.log('✅ Memory usage tests completed\n');
  }

  /**
   * Validate error handling performance
   */
  async validateErrorHandling() {
    console.log('⚠️ Testing Error Handling Performance...');
    
    const loadTester = new SkillGapLoadTester({ baseUrl: this.baseUrl });
    
    // Test invalid skill search
    const invalidSearchStart = Date.now();
    await loadTester.testSkillSearch('');
    const invalidSearchTime = Date.now() - invalidSearchStart;
    
    this.recordTest('Invalid Request Response Time',
      invalidSearchTime < 1000,
      `${invalidSearchTime}ms (requirement: <1000ms)`
    );

    // Test mixed valid/invalid requests
    const mixedRequests = [];
    for (let i = 0; i < 10; i++) {
      if (i % 3 === 0) {
        mixedRequests.push(loadTester.testSkillSearch(''));
      } else {
        mixedRequests.push(loadTester.testSkillSearch('JavaScript'));
      }
    }

    const mixedStart = Date.now();
    const mixedResults = await Promise.all(mixedRequests);
    const mixedTime = Date.now() - mixedStart;
    const averageMixedTime = mixedTime / mixedRequests.length;
    
    this.recordTest('Mixed Request Performance',
      averageMixedTime < 500,
      `${averageMixedTime.toFixed(2)}ms average (requirement: <500ms)`
    );

    console.log('✅ Error handling performance tests completed\n');
  }

  /**
   * Validate concurrent user performance
   */
  async validateConcurrentUsers() {
    console.log('👥 Testing Concurrent User Performance...');
    
    const userCounts = [1, 3, 5];
    const results = [];

    for (const userCount of userCounts) {
      const loadTester = new SkillGapLoadTester({
        baseUrl: this.baseUrl,
        concurrentUsers: userCount,
        testDuration: 10000,
        rampUpTime: 2000,
      });

      const result = await loadTester.runLoadTest();
      results.push({
        users: userCount,
        throughput: result.throughput,
        errorRate: result.errorRate,
      });
    }

    // Check that error rate stays low with more users
    const maxErrorRate = Math.max(...results.map(r => r.errorRate));
    this.recordTest('Concurrent User Error Rate',
      maxErrorRate < 10,
      `${maxErrorRate.toFixed(2)}% max (requirement: <10%)`
    );

    // Check that throughput scales reasonably
    const throughputIncrease = results[2].throughput / results[0].throughput;
    this.recordTest('Throughput Scaling',
      throughputIncrease > 1.5,
      `${throughputIncrease.toFixed(2)}x increase (requirement: >1.5x)`
    );

    console.log('✅ Concurrent user performance tests completed\n');
  }

  /**
   * Record a test result
   */
  recordTest(testName, passed, details) {
    const result = {
      name: testName,
      passed,
      details,
      timestamp: new Date().toISOString(),
    };

    this.results.tests.push(result);
    
    if (passed) {
      this.results.passed++;
      console.log(`✅ ${testName}: ${details}`);
    } else {
      this.results.failed++;
      console.log(`❌ ${testName}: ${details}`);
    }
  }

  /**
   * Generate final validation report
   */
  generateFinalReport() {
    const totalTests = this.results.passed + this.results.failed;
    const passRate = (this.results.passed / totalTests) * 100;
    
    this.results.summary = {
      totalTests,
      passed: this.results.passed,
      failed: this.results.failed,
      passRate: `${passRate.toFixed(2)}%`,
      validationPassed: this.results.failed === 0,
      timestamp: new Date().toISOString(),
    };

    console.log('\n📋 Performance Validation Summary:');
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${this.results.passed}`);
    console.log(`Failed: ${this.results.failed}`);
    console.log(`Pass Rate: ${passRate.toFixed(2)}%`);
    
    if (this.results.failed === 0) {
      console.log('🎉 All performance requirements met!');
    } else {
      console.log('⚠️ Some performance requirements not met. Review failed tests.');
    }
  }

  /**
   * Save validation report to file
   */
  saveReport(filename) {
    const outputPath = path.join(__dirname, '../../reports', filename);
    
    // Ensure reports directory exists
    const reportsDir = path.dirname(outputPath);
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    fs.writeFileSync(outputPath, JSON.stringify(this.results, null, 2));
    console.log(`\n📊 Validation report saved to: ${outputPath}`);
  }
}

// CLI execution
if (require.main === module) {
  const config = {
    baseUrl: process.env.TEST_BASE_URL || 'http://localhost:3000',
    requirements: {
      skillSearchResponseTime: parseInt(process.env.SKILL_SEARCH_THRESHOLD) || 500,
      skillAssessmentResponseTime: parseInt(process.env.SKILL_ASSESSMENT_THRESHOLD) || 1000,
      comprehensiveAnalysisResponseTime: parseInt(process.env.ANALYSIS_THRESHOLD) || 10000,
      maxErrorRate: parseFloat(process.env.MAX_ERROR_RATE) || 5,
      minThroughput: parseFloat(process.env.MIN_THROUGHPUT) || 1,
      maxMemoryIncrease: parseFloat(process.env.MAX_MEMORY_INCREASE) || 50,
    },
  };

  const validator = new PerformanceValidator(config);
  
  validator.validate()
    .then(results => {
      validator.saveReport(`performance-validation-${Date.now()}.json`);
      
      if (results.summary.validationPassed) {
        console.log('\n✅ Performance validation PASSED');
        process.exit(0);
      } else {
        console.log('\n❌ Performance validation FAILED');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('\n💥 Performance validation ERROR:', error);
      process.exit(1);
    });
}

module.exports = PerformanceValidator;
