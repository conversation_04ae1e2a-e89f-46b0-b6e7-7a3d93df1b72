#!/usr/bin/env node

/**
 * Load Testing Script for Skill Gap Analyzer
 * Comprehensive performance validation and stress testing
 */

const { performance } = require('perf_hooks');
const fetch = require('node-fetch');
const fs = require('fs');
const path = require('path');

class SkillGapLoadTester {
  constructor(config = {}) {
    this.baseUrl = config.baseUrl || 'http://localhost:3000';
    this.concurrentUsers = config.concurrentUsers || 10;
    this.testDuration = config.testDuration || 60000; // 1 minute
    this.rampUpTime = config.rampUpTime || 10000; // 10 seconds
    this.testScenarios = config.testScenarios || [
      'skill_search',
      'skill_assessment',
      'gap_analysis',
      'comprehensive_analysis'
    ];
    
    this.results = {
      startTime: null,
      endTime: null,
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      minResponseTime: Infinity,
      maxResponseTime: 0,
      responseTimePercentiles: {},
      throughput: 0,
      errorRate: 0,
      scenarios: {},
      memoryUsage: [],
      cpuUsage: [],
    };

    this.responseTimes = [];
    this.errors = [];
    this.activeUsers = 0;
  }

  /**
   * Generate test data for skill assessments
   */
  generateTestData() {
    const skills = [
      'JavaScript', 'Python', 'React', 'Node.js', 'SQL', 'AWS',
      'Docker', 'Kubernetes', 'Git', 'TypeScript', 'MongoDB', 'Redis',
      'GraphQL', 'REST APIs', 'Microservices', 'DevOps', 'CI/CD', 'Testing'
    ];

    const careerPaths = [
      'Full Stack Developer', 'Backend Developer', 'Frontend Developer',
      'DevOps Engineer', 'Data Scientist', 'Product Manager',
      'Software Architect', 'Cloud Engineer', 'Mobile Developer'
    ];

    return {
      skills: skills.slice(0, Math.floor(Math.random() * 10) + 5),
      careerPath: careerPaths[Math.floor(Math.random() * careerPaths.length)],
      assessments: skills.slice(0, Math.floor(Math.random() * 8) + 3).map(skill => ({
        skillName: skill,
        selfRating: Math.floor(Math.random() * 10) + 1,
        confidenceLevel: Math.floor(Math.random() * 10) + 1,
        yearsOfExperience: Math.floor(Math.random() * 10),
      }))
    };
  }

  /**
   * Simulate user authentication
   */
  async authenticateUser(userId) {
    const startTime = performance.now();
    
    try {
      const response = await fetch(`${this.baseUrl}/api/auth/session`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': `next-auth.session-token=test-token-${userId}`,
        },
      });

      const endTime = performance.now();
      const responseTime = endTime - startTime;

      return {
        success: response.ok,
        responseTime,
        status: response.status,
      };
    } catch (error) {
      const endTime = performance.now();
      return {
        success: false,
        responseTime: endTime - startTime,
        error: error.message,
      };
    }
  }

  /**
   * Test skill search endpoint
   */
  async testSkillSearch(query) {
    const startTime = performance.now();
    
    try {
      const response = await fetch(
        `${this.baseUrl}/api/skills/search?q=${encodeURIComponent(query)}`,
        {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
        }
      );

      const endTime = performance.now();
      const responseTime = endTime - startTime;
      const data = await response.json();

      return {
        success: response.ok,
        responseTime,
        status: response.status,
        resultCount: data.skills?.length || 0,
      };
    } catch (error) {
      const endTime = performance.now();
      return {
        success: false,
        responseTime: endTime - startTime,
        error: error.message,
      };
    }
  }

  /**
   * Test skill assessment submission
   */
  async testSkillAssessment(assessments, userId) {
    const startTime = performance.now();
    
    try {
      const response = await fetch(`${this.baseUrl}/api/skills/assessment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': `next-auth.session-token=test-token-${userId}`,
        },
        body: JSON.stringify({ assessments }),
      });

      const endTime = performance.now();
      const responseTime = endTime - startTime;

      return {
        success: response.ok,
        responseTime,
        status: response.status,
        assessmentCount: assessments.length,
      };
    } catch (error) {
      const endTime = performance.now();
      return {
        success: false,
        responseTime: endTime - startTime,
        error: error.message,
      };
    }
  }

  /**
   * Test comprehensive skill gap analysis
   */
  async testComprehensiveAnalysis(testData, userId) {
    const startTime = performance.now();
    
    try {
      const analysisRequest = {
        currentSkills: testData.assessments,
        targetCareerPath: {
          careerPathName: testData.careerPath,
          targetLevel: 'INTERMEDIATE',
        },
        preferences: {
          timeframe: 'SIX_MONTHS',
          hoursPerWeek: 10,
          learningStyle: 'MIXED',
          budgetRange: 'MODERATE',
          focusAreas: ['Technical Skills'],
        },
        includeMarketData: true,
        includePersonalizedPaths: true,
      };

      const response = await fetch(`${this.baseUrl}/api/ai/skills-analysis/comprehensive`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': `next-auth.session-token=test-token-${userId}`,
        },
        body: JSON.stringify(analysisRequest),
      });

      const endTime = performance.now();
      const responseTime = endTime - startTime;

      return {
        success: response.ok,
        responseTime,
        status: response.status,
        analysisType: 'comprehensive',
      };
    } catch (error) {
      const endTime = performance.now();
      return {
        success: false,
        responseTime: endTime - startTime,
        error: error.message,
      };
    }
  }

  /**
   * Simulate a complete user journey
   */
  async simulateUserJourney(userId) {
    const testData = this.generateTestData();
    const results = [];

    try {
      // 1. Authentication
      const authResult = await this.authenticateUser(userId);
      results.push({ scenario: 'authentication', ...authResult });

      // 2. Skill Search
      for (const skill of testData.skills.slice(0, 3)) {
        const searchResult = await this.testSkillSearch(skill);
        results.push({ scenario: 'skill_search', ...searchResult });
      }

      // 3. Skill Assessment
      const assessmentResult = await this.testSkillAssessment(testData.assessments, userId);
      results.push({ scenario: 'skill_assessment', ...assessmentResult });

      // 4. Comprehensive Analysis
      const analysisResult = await this.testComprehensiveAnalysis(testData, userId);
      results.push({ scenario: 'comprehensive_analysis', ...analysisResult });

      return results;
    } catch (error) {
      console.error(`User ${userId} journey failed:`, error);
      return results;
    }
  }

  /**
   * Record test result
   */
  recordResult(result) {
    this.results.totalRequests++;
    
    if (result.success) {
      this.results.successfulRequests++;
    } else {
      this.results.failedRequests++;
      this.errors.push({
        timestamp: Date.now(),
        error: result.error,
        status: result.status,
        scenario: result.scenario,
      });
    }

    this.responseTimes.push(result.responseTime);
    
    // Update min/max response times
    this.results.minResponseTime = Math.min(this.results.minResponseTime, result.responseTime);
    this.results.maxResponseTime = Math.max(this.results.maxResponseTime, result.responseTime);

    // Update scenario-specific metrics
    if (!this.results.scenarios[result.scenario]) {
      this.results.scenarios[result.scenario] = {
        requests: 0,
        successes: 0,
        failures: 0,
        totalResponseTime: 0,
        averageResponseTime: 0,
      };
    }

    const scenario = this.results.scenarios[result.scenario];
    scenario.requests++;
    scenario.totalResponseTime += result.responseTime;
    scenario.averageResponseTime = scenario.totalResponseTime / scenario.requests;
    
    if (result.success) {
      scenario.successes++;
    } else {
      scenario.failures++;
    }
  }

  /**
   * Calculate performance metrics
   */
  calculateMetrics() {
    const duration = this.results.endTime - this.results.startTime;
    
    // Calculate average response time
    this.results.averageResponseTime = this.responseTimes.reduce((a, b) => a + b, 0) / this.responseTimes.length;
    
    // Calculate throughput (requests per second)
    this.results.throughput = (this.results.totalRequests / duration) * 1000;
    
    // Calculate error rate
    this.results.errorRate = (this.results.failedRequests / this.results.totalRequests) * 100;
    
    // Calculate percentiles
    const sortedTimes = this.responseTimes.sort((a, b) => a - b);
    this.results.responseTimePercentiles = {
      p50: sortedTimes[Math.floor(sortedTimes.length * 0.5)],
      p75: sortedTimes[Math.floor(sortedTimes.length * 0.75)],
      p90: sortedTimes[Math.floor(sortedTimes.length * 0.9)],
      p95: sortedTimes[Math.floor(sortedTimes.length * 0.95)],
      p99: sortedTimes[Math.floor(sortedTimes.length * 0.99)],
    };
  }

  /**
   * Run load test
   */
  async runLoadTest() {
    console.log('🚀 Starting Skill Gap Analyzer Load Test...');
    console.log(`Configuration:
      - Base URL: ${this.baseUrl}
      - Concurrent Users: ${this.concurrentUsers}
      - Test Duration: ${this.testDuration}ms
      - Ramp-up Time: ${this.rampUpTime}ms
    `);

    this.results.startTime = Date.now();
    const promises = [];

    // Ramp up users gradually
    for (let i = 0; i < this.concurrentUsers; i++) {
      const delay = (this.rampUpTime / this.concurrentUsers) * i;
      
      promises.push(
        new Promise(resolve => {
          setTimeout(async () => {
            this.activeUsers++;
            const userId = `load-test-user-${i}`;
            const endTime = this.results.startTime + this.testDuration;
            
            while (Date.now() < endTime) {
              const journeyResults = await this.simulateUserJourney(userId);
              journeyResults.forEach(result => this.recordResult(result));
              
              // Small delay between journeys
              await new Promise(r => setTimeout(r, Math.random() * 1000 + 500));
            }
            
            this.activeUsers--;
            resolve();
          }, delay);
        })
      );
    }

    // Wait for all users to complete
    await Promise.all(promises);
    
    this.results.endTime = Date.now();
    this.calculateMetrics();
    
    return this.results;
  }

  /**
   * Generate performance report
   */
  generateReport() {
    const report = {
      summary: {
        testDuration: `${(this.results.endTime - this.results.startTime) / 1000}s`,
        totalRequests: this.results.totalRequests,
        successfulRequests: this.results.successfulRequests,
        failedRequests: this.results.failedRequests,
        errorRate: `${this.results.errorRate.toFixed(2)}%`,
        throughput: `${this.results.throughput.toFixed(2)} req/s`,
      },
      responseTime: {
        average: `${this.results.averageResponseTime.toFixed(2)}ms`,
        min: `${this.results.minResponseTime.toFixed(2)}ms`,
        max: `${this.results.maxResponseTime.toFixed(2)}ms`,
        percentiles: {
          p50: `${this.results.responseTimePercentiles.p50?.toFixed(2)}ms`,
          p75: `${this.results.responseTimePercentiles.p75?.toFixed(2)}ms`,
          p90: `${this.results.responseTimePercentiles.p90?.toFixed(2)}ms`,
          p95: `${this.results.responseTimePercentiles.p95?.toFixed(2)}ms`,
          p99: `${this.results.responseTimePercentiles.p99?.toFixed(2)}ms`,
        },
      },
      scenarios: this.results.scenarios,
      errors: this.errors.slice(0, 10), // Show first 10 errors
    };

    return report;
  }

  /**
   * Save results to file
   */
  saveResults(filename) {
    const report = this.generateReport();
    const outputPath = path.join(__dirname, '../../reports', filename);
    
    // Ensure reports directory exists
    const reportsDir = path.dirname(outputPath);
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    fs.writeFileSync(outputPath, JSON.stringify(report, null, 2));
    console.log(`📊 Load test report saved to: ${outputPath}`);
  }
}

// CLI execution
if (require.main === module) {
  const config = {
    baseUrl: process.env.TEST_BASE_URL || 'http://localhost:3000',
    concurrentUsers: parseInt(process.env.CONCURRENT_USERS) || 10,
    testDuration: parseInt(process.env.TEST_DURATION) || 60000,
    rampUpTime: parseInt(process.env.RAMP_UP_TIME) || 10000,
  };

  const tester = new SkillGapLoadTester(config);
  
  tester.runLoadTest()
    .then(results => {
      console.log('\n📈 Load Test Results:');
      console.log(JSON.stringify(tester.generateReport(), null, 2));
      
      tester.saveResults(`load-test-${Date.now()}.json`);
      
      // Exit with error code if error rate is too high
      if (results.errorRate > 5) {
        console.error(`❌ High error rate: ${results.errorRate.toFixed(2)}%`);
        process.exit(1);
      } else {
        console.log('✅ Load test completed successfully');
        process.exit(0);
      }
    })
    .catch(error => {
      console.error('❌ Load test failed:', error);
      process.exit(1);
    });
}

module.exports = SkillGapLoadTester;
