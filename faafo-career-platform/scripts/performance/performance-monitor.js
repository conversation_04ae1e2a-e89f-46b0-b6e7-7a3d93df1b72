#!/usr/bin/env node

/**
 * Performance Monitoring Script for Skill Gap Analyzer
 * Real-time performance metrics collection and analysis
 */

const { performance, PerformanceObserver } = require('perf_hooks');
const os = require('os');
const fs = require('fs');
const path = require('path');

class PerformanceMonitor {
  constructor(config = {}) {
    this.monitoringInterval = config.monitoringInterval || 5000; // 5 seconds
    this.maxDataPoints = config.maxDataPoints || 1000;
    this.alertThresholds = {
      responseTime: config.responseTimeThreshold || 2000, // 2 seconds
      errorRate: config.errorRateThreshold || 5, // 5%
      memoryUsage: config.memoryThreshold || 80, // 80%
      cpuUsage: config.cpuThreshold || 80, // 80%
      ...config.alertThresholds
    };

    this.metrics = {
      responseTime: [],
      throughput: [],
      errorRate: [],
      memoryUsage: [],
      cpuUsage: [],
      activeConnections: [],
      databaseConnections: [],
      cacheHitRate: [],
      aiServiceLatency: [],
    };

    this.alerts = [];
    this.isMonitoring = false;
    this.startTime = null;
  }

  /**
   * Start performance monitoring
   */
  start() {
    if (this.isMonitoring) {
      console.log('⚠️ Performance monitoring is already running');
      return;
    }

    console.log('🔍 Starting performance monitoring...');
    this.isMonitoring = true;
    this.startTime = Date.now();

    // Set up performance observers
    this.setupPerformanceObservers();

    // Start periodic monitoring
    this.monitoringTimer = setInterval(() => {
      this.collectMetrics();
    }, this.monitoringInterval);

    console.log(`✅ Performance monitoring started (interval: ${this.monitoringInterval}ms)`);
  }

  /**
   * Stop performance monitoring
   */
  stop() {
    if (!this.isMonitoring) {
      console.log('⚠️ Performance monitoring is not running');
      return;
    }

    console.log('🛑 Stopping performance monitoring...');
    this.isMonitoring = false;

    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer);
    }

    console.log('✅ Performance monitoring stopped');
    return this.generateReport();
  }

  /**
   * Set up performance observers for Node.js metrics
   */
  setupPerformanceObservers() {
    // HTTP request performance
    const httpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach(entry => {
        if (entry.name.includes('skill') || entry.name.includes('api')) {
          this.recordResponseTime(entry.duration);
        }
      });
    });

    try {
      httpObserver.observe({ entryTypes: ['measure', 'navigation'] });
    } catch (error) {
      console.warn('Could not set up HTTP performance observer:', error.message);
    }
  }

  /**
   * Collect system and application metrics
   */
  collectMetrics() {
    const timestamp = Date.now();

    // System metrics
    const memoryUsage = this.getMemoryUsage();
    const cpuUsage = this.getCPUUsage();

    // Application metrics (simulated - in real app these would come from actual monitoring)
    const responseTime = this.getAverageResponseTime();
    const throughput = this.getThroughput();
    const errorRate = this.getErrorRate();
    const cacheHitRate = this.getCacheHitRate();
    const aiServiceLatency = this.getAIServiceLatency();

    // Record metrics
    this.recordMetric('memoryUsage', memoryUsage, timestamp);
    this.recordMetric('cpuUsage', cpuUsage, timestamp);
    this.recordMetric('responseTime', responseTime, timestamp);
    this.recordMetric('throughput', throughput, timestamp);
    this.recordMetric('errorRate', errorRate, timestamp);
    this.recordMetric('cacheHitRate', cacheHitRate, timestamp);
    this.recordMetric('aiServiceLatency', aiServiceLatency, timestamp);

    // Check for alerts
    this.checkAlerts({
      memoryUsage,
      cpuUsage,
      responseTime,
      errorRate,
      aiServiceLatency,
    });

    // Log current status
    this.logCurrentStatus({
      memoryUsage,
      cpuUsage,
      responseTime,
      throughput,
      errorRate,
    });
  }

  /**
   * Get memory usage percentage
   */
  getMemoryUsage() {
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    return (usedMemory / totalMemory) * 100;
  }

  /**
   * Get CPU usage percentage (simplified)
   */
  getCPUUsage() {
    const cpus = os.cpus();
    let totalIdle = 0;
    let totalTick = 0;

    cpus.forEach(cpu => {
      for (const type in cpu.times) {
        totalTick += cpu.times[type];
      }
      totalIdle += cpu.times.idle;
    });

    const idle = totalIdle / cpus.length;
    const total = totalTick / cpus.length;
    const usage = 100 - ~~(100 * idle / total);

    return usage;
  }

  /**
   * Get average response time (simulated)
   */
  getAverageResponseTime() {
    // In a real implementation, this would aggregate actual response times
    return Math.random() * 1000 + 200; // 200-1200ms
  }

  /**
   * Get throughput (requests per second)
   */
  getThroughput() {
    // In a real implementation, this would count actual requests
    return Math.random() * 50 + 10; // 10-60 req/s
  }

  /**
   * Get error rate percentage
   */
  getErrorRate() {
    // In a real implementation, this would calculate actual error rate
    return Math.random() * 3; // 0-3%
  }

  /**
   * Get cache hit rate percentage
   */
  getCacheHitRate() {
    // In a real implementation, this would come from Redis/cache metrics
    return Math.random() * 20 + 75; // 75-95%
  }

  /**
   * Get AI service latency
   */
  getAIServiceLatency() {
    // In a real implementation, this would track actual AI service calls
    return Math.random() * 3000 + 500; // 500-3500ms
  }

  /**
   * Record a metric value
   */
  recordMetric(metricName, value, timestamp) {
    if (!this.metrics[metricName]) {
      this.metrics[metricName] = [];
    }

    this.metrics[metricName].push({
      value,
      timestamp,
    });

    // Keep only the last N data points
    if (this.metrics[metricName].length > this.maxDataPoints) {
      this.metrics[metricName].shift();
    }
  }

  /**
   * Record response time
   */
  recordResponseTime(duration) {
    this.recordMetric('responseTime', duration, Date.now());
  }

  /**
   * Check for performance alerts
   */
  checkAlerts(currentMetrics) {
    const alerts = [];

    // Response time alert
    if (currentMetrics.responseTime > this.alertThresholds.responseTime) {
      alerts.push({
        type: 'HIGH_RESPONSE_TIME',
        message: `Response time ${currentMetrics.responseTime.toFixed(2)}ms exceeds threshold ${this.alertThresholds.responseTime}ms`,
        severity: 'WARNING',
        timestamp: Date.now(),
      });
    }

    // Error rate alert
    if (currentMetrics.errorRate > this.alertThresholds.errorRate) {
      alerts.push({
        type: 'HIGH_ERROR_RATE',
        message: `Error rate ${currentMetrics.errorRate.toFixed(2)}% exceeds threshold ${this.alertThresholds.errorRate}%`,
        severity: 'CRITICAL',
        timestamp: Date.now(),
      });
    }

    // Memory usage alert
    if (currentMetrics.memoryUsage > this.alertThresholds.memoryUsage) {
      alerts.push({
        type: 'HIGH_MEMORY_USAGE',
        message: `Memory usage ${currentMetrics.memoryUsage.toFixed(2)}% exceeds threshold ${this.alertThresholds.memoryUsage}%`,
        severity: 'WARNING',
        timestamp: Date.now(),
      });
    }

    // CPU usage alert
    if (currentMetrics.cpuUsage > this.alertThresholds.cpuUsage) {
      alerts.push({
        type: 'HIGH_CPU_USAGE',
        message: `CPU usage ${currentMetrics.cpuUsage.toFixed(2)}% exceeds threshold ${this.alertThresholds.cpuUsage}%`,
        severity: 'WARNING',
        timestamp: Date.now(),
      });
    }

    // AI service latency alert
    if (currentMetrics.aiServiceLatency > 5000) {
      alerts.push({
        type: 'HIGH_AI_LATENCY',
        message: `AI service latency ${currentMetrics.aiServiceLatency.toFixed(2)}ms is very high`,
        severity: 'CRITICAL',
        timestamp: Date.now(),
      });
    }

    // Record and log alerts
    alerts.forEach(alert => {
      this.alerts.push(alert);
      console.log(`🚨 ALERT [${alert.severity}]: ${alert.message}`);
    });
  }

  /**
   * Log current performance status
   */
  logCurrentStatus(metrics) {
    const uptime = Date.now() - this.startTime;
    const uptimeMinutes = Math.floor(uptime / 60000);

    console.log(`📊 Performance Status (Uptime: ${uptimeMinutes}m):`);
    console.log(`   Memory: ${metrics.memoryUsage.toFixed(1)}%`);
    console.log(`   CPU: ${metrics.cpuUsage.toFixed(1)}%`);
    console.log(`   Response Time: ${metrics.responseTime.toFixed(1)}ms`);
    console.log(`   Throughput: ${metrics.throughput.toFixed(1)} req/s`);
    console.log(`   Error Rate: ${metrics.errorRate.toFixed(2)}%`);
    console.log('');
  }

  /**
   * Generate performance report
   */
  generateReport() {
    const endTime = Date.now();
    const duration = endTime - this.startTime;

    const report = {
      summary: {
        monitoringDuration: `${Math.floor(duration / 60000)}m ${Math.floor((duration % 60000) / 1000)}s`,
        totalAlerts: this.alerts.length,
        criticalAlerts: this.alerts.filter(a => a.severity === 'CRITICAL').length,
        warningAlerts: this.alerts.filter(a => a.severity === 'WARNING').length,
      },
      metrics: {},
      alerts: this.alerts,
      recommendations: this.generateRecommendations(),
    };

    // Calculate metric summaries
    Object.keys(this.metrics).forEach(metricName => {
      const values = this.metrics[metricName].map(m => m.value);
      if (values.length > 0) {
        report.metrics[metricName] = {
          average: values.reduce((a, b) => a + b, 0) / values.length,
          min: Math.min(...values),
          max: Math.max(...values),
          latest: values[values.length - 1],
          dataPoints: values.length,
        };
      }
    });

    return report;
  }

  /**
   * Generate performance recommendations
   */
  generateRecommendations() {
    const recommendations = [];

    // Check average response time
    const avgResponseTime = this.metrics.responseTime.length > 0 
      ? this.metrics.responseTime.reduce((sum, m) => sum + m.value, 0) / this.metrics.responseTime.length
      : 0;

    if (avgResponseTime > 1000) {
      recommendations.push({
        type: 'PERFORMANCE',
        priority: 'HIGH',
        message: 'Average response time is high. Consider implementing caching, optimizing database queries, or scaling infrastructure.',
      });
    }

    // Check error rate
    const avgErrorRate = this.metrics.errorRate.length > 0
      ? this.metrics.errorRate.reduce((sum, m) => sum + m.value, 0) / this.metrics.errorRate.length
      : 0;

    if (avgErrorRate > 2) {
      recommendations.push({
        type: 'RELIABILITY',
        priority: 'CRITICAL',
        message: 'Error rate is elevated. Review error logs and implement better error handling.',
      });
    }

    // Check memory usage
    const avgMemoryUsage = this.metrics.memoryUsage.length > 0
      ? this.metrics.memoryUsage.reduce((sum, m) => sum + m.value, 0) / this.metrics.memoryUsage.length
      : 0;

    if (avgMemoryUsage > 70) {
      recommendations.push({
        type: 'RESOURCE',
        priority: 'MEDIUM',
        message: 'Memory usage is high. Consider optimizing memory usage or increasing available memory.',
      });
    }

    return recommendations;
  }

  /**
   * Save monitoring report to file
   */
  saveReport(filename) {
    const report = this.generateReport();
    const outputPath = path.join(__dirname, '../../reports', filename);
    
    // Ensure reports directory exists
    const reportsDir = path.dirname(outputPath);
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    fs.writeFileSync(outputPath, JSON.stringify(report, null, 2));
    console.log(`📊 Performance report saved to: ${outputPath}`);
    
    return report;
  }
}

// CLI execution
if (require.main === module) {
  const monitor = new PerformanceMonitor({
    monitoringInterval: parseInt(process.env.MONITORING_INTERVAL) || 5000,
    responseTimeThreshold: parseInt(process.env.RESPONSE_TIME_THRESHOLD) || 2000,
    errorRateThreshold: parseInt(process.env.ERROR_RATE_THRESHOLD) || 5,
  });

  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Received SIGINT, stopping performance monitoring...');
    const report = monitor.stop();
    monitor.saveReport(`performance-report-${Date.now()}.json`);
    
    console.log('\n📈 Final Performance Report:');
    console.log(JSON.stringify(report, null, 2));
    
    process.exit(0);
  });

  // Start monitoring
  monitor.start();
  
  console.log('Press Ctrl+C to stop monitoring and generate report');
}

module.exports = PerformanceMonitor;
