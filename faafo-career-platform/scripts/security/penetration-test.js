#!/usr/bin/env node

/**
 * Penetration Testing Script for Skill Gap Analyzer
 * Advanced security testing with attack simulation
 */

const fetch = require('node-fetch');
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

class PenetrationTester {
  constructor(config = {}) {
    this.baseUrl = config.baseUrl || 'http://localhost:3000';
    this.attackVectors = [];
    this.exploits = [];
    this.results = {
      attacksAttempted: 0,
      attacksSuccessful: 0,
      vulnerabilitiesExploited: 0,
      securityBypassAttempts: 0,
      dataExtractionAttempts: 0,
      summary: {},
    };
  }

  /**
   * Run comprehensive penetration testing
   */
  async runPenetrationTest() {
    console.log('⚔️ Starting Penetration Testing for Skill Gap Analyzer');
    console.log(`Target: ${this.baseUrl}`);
    console.log('⚠️ WARNING: This is an authorized security test');
    console.log('');

    try {
      // Authentication Bypass Attacks
      await this.testAuthenticationBypass();
      
      // Authorization Bypass Attacks
      await this.testAuthorizationBypass();
      
      // Injection Attacks
      await this.testInjectionAttacks();
      
      // Business Logic Attacks
      await this.testBusinessLogicFlaws();
      
      // Session Management Attacks
      await this.testSessionAttacks();
      
      // Data Extraction Attacks
      await this.testDataExtraction();
      
      // API Abuse Attacks
      await this.testAPIAbuse();
      
      // Infrastructure Attacks
      await this.testInfrastructureAttacks();
      
      // Generate penetration test report
      this.generatePenetrationReport();
      
      return this.results;
    } catch (error) {
      console.error('❌ Penetration test failed:', error);
      throw error;
    }
  }

  /**
   * Test authentication bypass techniques
   */
  async testAuthenticationBypass() {
    console.log('🔓 Testing Authentication Bypass Attacks...');
    
    // Test 1: JWT token manipulation
    await this.testJWTManipulation();
    
    // Test 2: Session fixation
    await this.testSessionFixation();
    
    // Test 3: Cookie manipulation
    await this.testCookieManipulation();
    
    // Test 4: Header injection
    await this.testHeaderInjection();
    
    console.log('✅ Authentication bypass tests completed\n');
  }

  /**
   * Test authorization bypass techniques
   */
  async testAuthorizationBypass() {
    console.log('🛡️ Testing Authorization Bypass Attacks...');
    
    // Test 1: Horizontal privilege escalation
    await this.testHorizontalPrivilegeEscalation();
    
    // Test 2: Vertical privilege escalation
    await this.testVerticalPrivilegeEscalation();
    
    // Test 3: Direct object reference manipulation
    await this.testDirectObjectReferenceAttacks();
    
    // Test 4: Parameter pollution
    await this.testParameterPollution();
    
    console.log('✅ Authorization bypass tests completed\n');
  }

  /**
   * Test injection attacks
   */
  async testInjectionAttacks() {
    console.log('💉 Testing Injection Attacks...');
    
    // Test 1: Advanced SQL injection
    await this.testAdvancedSQLInjection();
    
    // Test 2: NoSQL injection
    await this.testNoSQLInjection();
    
    // Test 3: Command injection
    await this.testCommandInjection();
    
    // Test 4: LDAP injection
    await this.testLDAPInjection();
    
    // Test 5: Template injection
    await this.testTemplateInjection();
    
    console.log('✅ Injection attack tests completed\n');
  }

  /**
   * Test business logic flaws
   */
  async testBusinessLogicFlaws() {
    console.log('🧠 Testing Business Logic Attacks...');
    
    // Test 1: Race conditions
    await this.testRaceConditions();
    
    // Test 2: Workflow bypass
    await this.testWorkflowBypass();
    
    // Test 3: Rate limiting bypass
    await this.testRateLimitingBypass();
    
    // Test 4: Price manipulation
    await this.testPriceManipulation();
    
    console.log('✅ Business logic attack tests completed\n');
  }

  /**
   * Test session management attacks
   */
  async testSessionAttacks() {
    console.log('🍪 Testing Session Management Attacks...');
    
    // Test 1: Session hijacking
    await this.testSessionHijacking();
    
    // Test 2: Session replay
    await this.testSessionReplay();
    
    // Test 3: Cross-site request forgery
    await this.testAdvancedCSRF();
    
    console.log('✅ Session attack tests completed\n');
  }

  /**
   * Test data extraction attacks
   */
  async testDataExtraction() {
    console.log('📊 Testing Data Extraction Attacks...');
    
    // Test 1: Information disclosure
    await this.testInformationDisclosure();
    
    // Test 2: Directory traversal
    await this.testDirectoryTraversal();
    
    // Test 3: File inclusion
    await this.testFileInclusion();
    
    // Test 4: XXE attacks
    await this.testXXEAttacks();
    
    console.log('✅ Data extraction attack tests completed\n');
  }

  /**
   * Test API abuse attacks
   */
  async testAPIAbuse() {
    console.log('🔌 Testing API Abuse Attacks...');
    
    // Test 1: Mass assignment
    await this.testMassAssignment();
    
    // Test 2: API enumeration
    await this.testAPIEnumeration();
    
    // Test 3: GraphQL attacks
    await this.testGraphQLAttacks();
    
    // Test 4: REST API abuse
    await this.testRESTAPIAbuse();
    
    console.log('✅ API abuse attack tests completed\n');
  }

  /**
   * Specific attack implementations
   */
  async testJWTManipulation() {
    this.results.attacksAttempted++;
    
    // Test JWT none algorithm attack
    const maliciousJWT = this.createMaliciousJWT();
    const response = await this.makeRequest('/api/skills/assessment', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${maliciousJWT}`,
        'Content-Type': 'application/json',
      },
    });

    if (response.status === 200) {
      this.recordExploit('JWT_MANIPULATION', 'CRITICAL',
        'JWT none algorithm attack successful - authentication bypassed');
      this.results.attacksSuccessful++;
    } else {
      this.recordAttackVector('JWT Manipulation', false,
        'JWT manipulation properly blocked');
    }
  }

  async testAdvancedSQLInjection() {
    this.results.attacksAttempted++;
    
    const advancedPayloads = [
      "'; WAITFOR DELAY '00:00:05'; --",
      "' AND (SELECT COUNT(*) FROM information_schema.tables) > 0 --",
      "' UNION SELECT table_name FROM information_schema.tables --",
      "'; EXEC xp_cmdshell('whoami'); --",
      "' OR SLEEP(5) --",
    ];

    for (const payload of advancedPayloads) {
      const startTime = Date.now();
      const response = await this.makeRequest(`/api/skills/search?q=${encodeURIComponent(payload)}`);
      const responseTime = Date.now() - startTime;

      // Check for time-based SQL injection
      if (responseTime > 5000) {
        this.recordExploit('TIME_BASED_SQL_INJECTION', 'CRITICAL',
          `Time-based SQL injection detected with payload: ${payload}`);
        this.results.attacksSuccessful++;
        break;
      }
    }
  }

  async testRaceConditions() {
    this.results.attacksAttempted++;
    
    // Simulate concurrent skill assessment submissions
    const promises = [];
    const testData = {
      skillName: 'JavaScript',
      selfRating: 10,
      confidenceLevel: 10,
      yearsOfExperience: 5,
    };

    for (let i = 0; i < 50; i++) {
      promises.push(this.makeRequest('/api/skills/assessment', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testData),
      }));
    }

    const responses = await Promise.all(promises);
    const successfulResponses = responses.filter(r => r.status === 200);

    if (successfulResponses.length > 1) {
      this.recordExploit('RACE_CONDITION', 'HIGH',
        `Race condition detected - ${successfulResponses.length} concurrent submissions succeeded`);
      this.results.attacksSuccessful++;
    }
  }

  async testDirectoryTraversal() {
    this.results.attacksAttempted++;
    this.results.dataExtractionAttempts++;
    
    const traversalPayloads = [
      '../../../etc/passwd',
      '..\\..\\..\\windows\\system32\\drivers\\etc\\hosts',
      '....//....//....//etc/passwd',
      '%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd',
      '..%252f..%252f..%252fetc%252fpasswd',
    ];

    for (const payload of traversalPayloads) {
      const response = await this.makeRequest(`/api/files/${encodeURIComponent(payload)}`);
      
      if (response.status === 200) {
        const content = await response.text();
        if (content.includes('root:') || content.includes('localhost')) {
          this.recordExploit('DIRECTORY_TRAVERSAL', 'HIGH',
            `Directory traversal successful with payload: ${payload}`);
          this.results.attacksSuccessful++;
          break;
        }
      }
    }
  }

  async testMassAssignment() {
    this.results.attacksAttempted++;
    
    // Test mass assignment in skill assessment
    const maliciousData = {
      skillName: 'JavaScript',
      selfRating: 5,
      confidenceLevel: 5,
      yearsOfExperience: 2,
      // Malicious fields
      isAdmin: true,
      role: 'admin',
      userId: 'admin-user-id',
      permissions: ['admin', 'super-user'],
    };

    const response = await this.makeRequest('/api/skills/assessment', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(maliciousData),
    });

    if (response.status === 200) {
      const data = await response.json();
      if (data.isAdmin || data.role === 'admin') {
        this.recordExploit('MASS_ASSIGNMENT', 'HIGH',
          'Mass assignment vulnerability - unauthorized fields accepted');
        this.results.attacksSuccessful++;
      }
    }
  }

  async testAPIEnumeration() {
    this.results.attacksAttempted++;
    
    // Test for hidden API endpoints
    const potentialEndpoints = [
      '/api/admin',
      '/api/debug',
      '/api/internal',
      '/api/test',
      '/api/v2',
      '/api/backup',
      '/api/config',
      '/api/users/all',
      '/api/skills/admin',
      '/api/analytics/raw',
    ];

    let foundEndpoints = 0;
    for (const endpoint of potentialEndpoints) {
      const response = await this.makeRequest(endpoint);
      
      if (response.status !== 404 && response.status !== 403) {
        this.recordExploit('API_ENUMERATION', 'MEDIUM',
          `Hidden API endpoint discovered: ${endpoint}`);
        foundEndpoints++;
      }
    }

    if (foundEndpoints > 0) {
      this.results.attacksSuccessful++;
    }
  }

  /**
   * Helper methods
   */
  createMaliciousJWT() {
    // Create a JWT with "none" algorithm
    const header = { alg: 'none', typ: 'JWT' };
    const payload = { 
      sub: 'admin', 
      role: 'admin', 
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600,
    };

    const encodedHeader = Buffer.from(JSON.stringify(header)).toString('base64url');
    const encodedPayload = Buffer.from(JSON.stringify(payload)).toString('base64url');
    
    return `${encodedHeader}.${encodedPayload}.`;
  }

  async makeRequest(endpoint, options = {}) {
    try {
      const url = endpoint.startsWith('http') ? endpoint : `${this.baseUrl}${endpoint}`;
      return await fetch(url, {
        timeout: 10000,
        ...options,
      });
    } catch (error) {
      return {
        ok: false,
        status: 0,
        statusText: error.message,
        headers: new Map(),
        text: () => Promise.resolve(''),
        json: () => Promise.resolve({}),
      };
    }
  }

  recordExploit(type, severity, description) {
    const exploit = {
      type,
      severity,
      description,
      timestamp: new Date().toISOString(),
    };

    this.exploits.push(exploit);
    this.results.vulnerabilitiesExploited++;

    console.log(`🚨 ${severity} EXPLOIT: ${description}`);
  }

  recordAttackVector(attackName, successful, details) {
    const attack = {
      name: attackName,
      successful,
      details,
      timestamp: new Date().toISOString(),
    };

    this.attackVectors.push(attack);

    if (successful) {
      console.log(`💥 ${attackName}: ${details}`);
    } else {
      console.log(`🛡️ ${attackName}: ${details}`);
    }
  }

  generatePenetrationReport() {
    const successRate = this.results.attacksAttempted > 0 
      ? (this.results.attacksSuccessful / this.results.attacksAttempted) * 100 
      : 0;
    
    this.results.summary = {
      attacksAttempted: this.results.attacksAttempted,
      attacksSuccessful: this.results.attacksSuccessful,
      successRate: `${successRate.toFixed(2)}%`,
      vulnerabilitiesExploited: this.results.vulnerabilitiesExploited,
      securityPosture: this.calculateSecurityPosture(),
      riskLevel: this.calculateRiskLevel(),
      timestamp: new Date().toISOString(),
    };

    console.log('\n⚔️ Penetration Test Summary:');
    console.log(`Attacks Attempted: ${this.results.attacksAttempted}`);
    console.log(`Attacks Successful: ${this.results.attacksSuccessful}`);
    console.log(`Success Rate: ${successRate.toFixed(2)}%`);
    console.log(`Vulnerabilities Exploited: ${this.results.vulnerabilitiesExploited}`);
    console.log(`Security Posture: ${this.results.summary.securityPosture}`);
    console.log(`Risk Level: ${this.results.summary.riskLevel}`);
  }

  calculateSecurityPosture() {
    const successRate = this.results.attacksAttempted > 0 
      ? (this.results.attacksSuccessful / this.results.attacksAttempted) * 100 
      : 0;
    
    if (successRate === 0) return 'EXCELLENT';
    if (successRate < 10) return 'GOOD';
    if (successRate < 25) return 'FAIR';
    if (successRate < 50) return 'POOR';
    return 'CRITICAL';
  }

  calculateRiskLevel() {
    const criticalExploits = this.exploits.filter(e => e.severity === 'CRITICAL').length;
    const highExploits = this.exploits.filter(e => e.severity === 'HIGH').length;
    
    if (criticalExploits > 0) return 'CRITICAL';
    if (highExploits > 0) return 'HIGH';
    if (this.exploits.length > 0) return 'MEDIUM';
    return 'LOW';
  }

  saveReport(filename) {
    const report = {
      summary: this.results.summary,
      exploits: this.exploits,
      attackVectors: this.attackVectors,
      recommendations: this.generateRecommendations(),
      methodology: this.getTestMethodology(),
    };

    const outputPath = path.join(__dirname, '../../reports', filename);
    
    // Ensure reports directory exists
    const reportsDir = path.dirname(outputPath);
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    fs.writeFileSync(outputPath, JSON.stringify(report, null, 2));
    console.log(`\n📊 Penetration test report saved to: ${outputPath}`);
  }

  generateRecommendations() {
    const recommendations = [];

    if (this.exploits.some(e => e.severity === 'CRITICAL')) {
      recommendations.push({
        priority: 'CRITICAL',
        message: 'Critical vulnerabilities found - immediate remediation required',
        actions: ['Patch critical vulnerabilities', 'Review security architecture', 'Implement additional controls'],
      });
    }

    if (this.exploits.some(e => e.type === 'JWT_MANIPULATION')) {
      recommendations.push({
        priority: 'HIGH',
        message: 'Implement proper JWT validation and avoid none algorithm',
        actions: ['Use strong JWT signing algorithms', 'Validate JWT signatures', 'Implement proper token expiration'],
      });
    }

    return recommendations;
  }

  getTestMethodology() {
    return {
      scope: 'Skill Gap Analyzer API and Web Application',
      approach: 'Black-box and Gray-box testing',
      tools: ['Custom penetration testing scripts', 'Manual testing', 'Automated vulnerability scanning'],
      standards: ['OWASP Top 10', 'NIST Cybersecurity Framework', 'SANS Top 25'],
    };
  }

  // Placeholder methods for additional tests
  async testSessionFixation() { /* Implementation */ }
  async testCookieManipulation() { /* Implementation */ }
  async testHeaderInjection() { /* Implementation */ }
  async testHorizontalPrivilegeEscalation() { /* Implementation */ }
  async testVerticalPrivilegeEscalation() { /* Implementation */ }
  async testDirectObjectReferenceAttacks() { /* Implementation */ }
  async testParameterPollution() { /* Implementation */ }
  async testNoSQLInjection() { /* Implementation */ }
  async testCommandInjection() { /* Implementation */ }
  async testLDAPInjection() { /* Implementation */ }
  async testTemplateInjection() { /* Implementation */ }
  async testWorkflowBypass() { /* Implementation */ }
  async testRateLimitingBypass() { /* Implementation */ }
  async testPriceManipulation() { /* Implementation */ }
  async testSessionHijacking() { /* Implementation */ }
  async testSessionReplay() { /* Implementation */ }
  async testAdvancedCSRF() { /* Implementation */ }
  async testInformationDisclosure() { /* Implementation */ }
  async testFileInclusion() { /* Implementation */ }
  async testXXEAttacks() { /* Implementation */ }
  async testGraphQLAttacks() { /* Implementation */ }
  async testRESTAPIAbuse() { /* Implementation */ }
  async testInfrastructureAttacks() { /* Implementation */ }
}

// CLI execution
if (require.main === module) {
  const config = {
    baseUrl: process.env.TEST_BASE_URL || 'http://localhost:3000',
  };

  const tester = new PenetrationTester(config);
  
  tester.runPenetrationTest()
    .then(results => {
      tester.saveReport(`penetration-test-${Date.now()}.json`);
      
      if (results.vulnerabilitiesExploited > 0) {
        console.log('\n❌ Penetration test FAILED - Vulnerabilities exploited');
        process.exit(1);
      } else {
        console.log('\n✅ Penetration test PASSED - No vulnerabilities exploited');
        process.exit(0);
      }
    })
    .catch(error => {
      console.error('\n💥 Penetration test ERROR:', error);
      process.exit(1);
    });
}

module.exports = PenetrationTester;
