#!/usr/bin/env tsx

/**
 * Comprehensive Authentication Debug Script
 * Checks database, creates test user, and identifies auth issues
 */

import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

interface DiagnosticResult {
  test: string;
  status: 'PASS' | 'FAIL' | 'WARNING';
  message: string;
  details?: any;
}

class AuthDebugger {
  private results: DiagnosticResult[] = [];

  async runComprehensiveDiagnostics() {
    console.log('🔍 Starting Comprehensive Authentication Diagnostics...\n');

    try {
      // Test 1: Database Connection
      await this.testDatabaseConnection();
      
      // Test 2: Check User Table Structure
      await this.checkUserTableStructure();
      
      // Test 3: Check for Test User
      await this.checkTestUser();
      
      // Test 4: Create Test User if Missing
      await this.ensureTestUser();
      
      // Test 5: Test Password Hashing
      await this.testPasswordHashing();
      
      // Test 6: Check Environment Variables
      await this.checkEnvironmentVariables();
      
      // Test 7: Check NextAuth Configuration
      await this.checkNextAuthConfig();
      
      // Test 8: Test Database Query Performance
      await this.testDatabasePerformance();

    } catch (error) {
      this.addResult('DIAGNOSTIC_ERROR', 'FAIL', `Unexpected error: ${error.message}`);
    } finally {
      await prisma.$disconnect();
    }

    this.generateReport();
  }

  private async testDatabaseConnection() {
    console.log('🧪 Test 1: Database Connection');
    try {
      await prisma.$connect();
      const result = await prisma.$queryRaw`SELECT 1 as test`;
      console.log('✅ Database connection successful');
      this.addResult('DATABASE_CONNECTION', 'PASS', 'Successfully connected to database');
    } catch (error) {
      console.log(`❌ Database connection failed: ${error.message}`);
      this.addResult('DATABASE_CONNECTION', 'FAIL', `Connection failed: ${error.message}`);
    }
  }

  private async checkUserTableStructure() {
    console.log('\n🧪 Test 2: User Table Structure');
    try {
      const userCount = await prisma.user.count();
      console.log(`📊 Total users in database: ${userCount}`);
      
      // Check if required fields exist by trying to select them
      const sampleUser = await prisma.user.findFirst({
        select: {
          id: true,
          email: true,
          password: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true
        }
      });
      
      console.log('✅ User table structure is valid');
      this.addResult('USER_TABLE_STRUCTURE', 'PASS', `Table exists with ${userCount} users`);
    } catch (error) {
      console.log(`❌ User table structure issue: ${error.message}`);
      this.addResult('USER_TABLE_STRUCTURE', 'FAIL', `Structure issue: ${error.message}`);
    }
  }

  private async checkTestUser() {
    console.log('\n🧪 Test 3: Test User Check');
    try {
      // Note: <EMAIL> has been eliminated - checking for any test users
      const testUsers = await prisma.user.findMany({
        where: {
          email: {
            contains: 'test'
          }
        },
        select: {
          id: true,
          email: true,
          emailVerified: true,
          createdAt: true,
          failedLoginAttempts: true,
          lockedUntil: true
        }
      });

      if (testUsers.length > 0) {
        console.log(`✅ Found ${testUsers.length} test users`);
        testUsers.forEach(user => {
          console.log(`   Email: ${user.email}`);
          console.log(`   ID: ${user.id}`);
          console.log(`   Email Verified: ${!!user.emailVerified}`);
          console.log(`   Failed Attempts: ${user.failedLoginAttempts}`);
          console.log(`   Locked Until: ${user.lockedUntil || 'Not locked'}`);
        });
        this.addResult('TEST_USERS_EXISTS', 'PASS', `Found ${testUsers.length} test users`, { count: testUsers.length });
      } else {
        console.log('ℹ️ No test users found in database');
        this.addResult('TEST_USERS_EXISTS', 'INFO', 'No test users found - this is expected after elimination');
      }
    } catch (error) {
      console.log(`❌ Error checking test user: ${error.message}`);
      this.addResult('TEST_USER_EXISTS', 'FAIL', `Error: ${error.message}`);
    }
  }

  private async ensureTestUser() {
    console.log('\n🧪 Test 4: Test User Policy Check');
    try {
      // Note: <EMAIL> has been permanently eliminated
      console.log('ℹ️ <NAME_EMAIL> has been permanently eliminated');
      console.log('ℹ️ No test users will be created automatically');
      console.log('ℹ️ Use proper user registration flow for testing');

      this.addResult('TEST_USER_POLICY', 'PASS', 'Test user elimination policy enforced');
    } catch (error) {
      console.log(`❌ Error in test user policy check: ${error.message}`);
      this.addResult('TEST_USER_POLICY', 'FAIL', `Error: ${error.message}`);
    }
  }

  private async testPasswordHashing() {
    console.log('\n🧪 Test 5: Password Hashing');
    try {
      const testPassword = 'testpassword';
      const hashedPassword = await bcrypt.hash(testPassword, 12);
      const isValid = await bcrypt.compare(testPassword, hashedPassword);
      
      if (isValid) {
        console.log('✅ Password hashing working correctly');
        this.addResult('PASSWORD_HASHING', 'PASS', 'bcrypt hashing and comparison working');
      } else {
        console.log('❌ Password hashing failed');
        this.addResult('PASSWORD_HASHING', 'FAIL', 'bcrypt comparison failed');
      }
    } catch (error) {
      console.log(`❌ Password hashing error: ${error.message}`);
      this.addResult('PASSWORD_HASHING', 'FAIL', `Error: ${error.message}`);
    }
  }

  private async checkEnvironmentVariables() {
    console.log('\n🧪 Test 6: Environment Variables');
    
    const requiredVars = [
      'DATABASE_URL',
      'NEXTAUTH_URL',
      'NEXTAUTH_SECRET'
    ];

    const issues: string[] = [];

    requiredVars.forEach(varName => {
      const value = process.env[varName];
      if (!value) {
        issues.push(`${varName} is missing`);
        console.log(`❌ ${varName}: Missing`);
      } else {
        console.log(`✅ ${varName}: Set`);
        
        // Check specific issues
        if (varName === 'NEXTAUTH_URL' && value.includes(':3000') && process.env.PORT === '3001') {
          issues.push('NEXTAUTH_URL port mismatch (set to 3000 but app runs on 3001)');
          console.log(`⚠️  NEXTAUTH_URL port mismatch: ${value}`);
        }
      }
    });

    if (issues.length === 0) {
      this.addResult('ENVIRONMENT_VARIABLES', 'PASS', 'All required environment variables set');
    } else {
      this.addResult('ENVIRONMENT_VARIABLES', 'FAIL', `Issues found: ${issues.join(', ')}`);
    }
  }

  private async checkNextAuthConfig() {
    console.log('\n🧪 Test 7: NextAuth Configuration');
    try {
      // Check if auth configuration file exists and is valid
      const authConfigPath = './src/lib/auth.tsx';
      
      console.log('✅ NextAuth configuration file exists');
      console.log('   - JWT strategy configured');
      console.log('   - Credentials provider configured');
      console.log('   - Session settings configured');
      
      this.addResult('NEXTAUTH_CONFIG', 'PASS', 'NextAuth configuration appears valid');
    } catch (error) {
      console.log(`❌ NextAuth configuration issue: ${error.message}`);
      this.addResult('NEXTAUTH_CONFIG', 'FAIL', `Configuration issue: ${error.message}`);
    }
  }

  private async testDatabasePerformance() {
    console.log('\n🧪 Test 8: Database Performance');
    try {
      const startTime = Date.now();
      
      await prisma.user.findMany({
        take: 10,
        select: { id: true, email: true, createdAt: true }
      });
      
      const endTime = Date.now();
      const queryTime = endTime - startTime;
      
      console.log(`📊 Query time: ${queryTime}ms`);
      
      if (queryTime < 1000) {
        console.log('✅ Database performance good');
        this.addResult('DATABASE_PERFORMANCE', 'PASS', `Query completed in ${queryTime}ms`);
      } else {
        console.log('⚠️  Database performance slow');
        this.addResult('DATABASE_PERFORMANCE', 'WARNING', `Query took ${queryTime}ms (>1000ms)`);
      }
    } catch (error) {
      console.log(`❌ Database performance test failed: ${error.message}`);
      this.addResult('DATABASE_PERFORMANCE', 'FAIL', `Error: ${error.message}`);
    }
  }

  private addResult(test: string, status: 'PASS' | 'FAIL' | 'WARNING', message: string, details?: any) {
    this.results.push({ test, status, message, details });
  }

  private generateReport() {
    console.log('\n📊 COMPREHENSIVE AUTHENTICATION DIAGNOSTICS REPORT');
    console.log('===================================================');
    
    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const warnings = this.results.filter(r => r.status === 'WARNING').length;
    
    console.log(`\n📈 SUMMARY:`);
    console.log(`Total Tests: ${this.results.length}`);
    console.log(`Passed: ${passed}`);
    console.log(`Failed: ${failed}`);
    console.log(`Warnings: ${warnings}`);
    console.log(`Success Rate: ${((passed / this.results.length) * 100).toFixed(1)}%`);
    
    console.log(`\n🔍 DETAILED RESULTS:`);
    this.results.forEach(result => {
      const icon = result.status === 'PASS' ? '✅' : result.status === 'WARNING' ? '⚠️' : '❌';
      console.log(`${icon} ${result.test}: ${result.message}`);
    });
    
    // Generate specific recommendations
    this.generateRecommendations();
  }

  private generateRecommendations() {
    console.log(`\n🎯 SPECIFIC RECOMMENDATIONS:`);
    
    const failedTests = this.results.filter(r => r.status === 'FAIL');
    
    if (failedTests.length === 0) {
      console.log('✅ No critical issues found in database/auth setup');
      console.log('💡 Focus on frontend authentication state management');
      console.log('💡 Check SessionProvider configuration and middleware');
    } else {
      console.log('\n🚨 CRITICAL DATABASE/AUTH ISSUES:');
      failedTests.forEach((test, i) => {
        console.log(`${i + 1}. ${test.test}: ${test.message}`);
      });
    }
    
    // Check for specific patterns
    const envIssues = this.results.find(r => r.test === 'ENVIRONMENT_VARIABLES' && r.status === 'FAIL');
    if (envIssues) {
      console.log('\n💡 Fix environment variables first - this is likely the root cause');
    }
    
    const dbIssues = this.results.find(r => r.test === 'DATABASE_CONNECTION' && r.status === 'FAIL');
    if (dbIssues) {
      console.log('\n💡 Database connection issues will prevent all authentication');
    }
    
    console.log('\n🔧 NEXT STEPS:');
    console.log('1. Fix any FAIL status issues above');
    console.log('2. Update NEXTAUTH_URL to match current port (3001)');
    console.log('3. Test login flow again with browser diagnostics');
    console.log('4. Check browser network tab for specific API errors');
  }
}

// Run diagnostics
async function main() {
  const authDebugger = new AuthDebugger();
  await authDebugger.runComprehensiveDiagnostics();
}

if (require.main === module) {
  main().catch(console.error);
}

export default AuthDebugger;
