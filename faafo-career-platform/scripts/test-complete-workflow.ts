import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

interface TestResult {
  step: string;
  status: 'PASS' | 'FAIL' | 'ERROR';
  message: string;
  data?: any;
}

class ComprehensiveWorkflowTester {
  private results: TestResult[] = [];
  private userId: string = '';
  private baseUrl = 'http://localhost:3000';

  private addResult(step: string, status: 'PASS' | 'FAIL' | 'ERROR', message: string, data?: any) {
    this.results.push({ step, status, message, data });
    const emoji = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
    console.log(`${emoji} ${step}: ${message}`);
    if (data) {
      console.log(`   Data:`, JSON.stringify(data, null, 2));
    }
  }

  async testCompleteWorkflow() {
    console.log('🚀 Starting COMPREHENSIVE End-to-End Workflow Testing');
    console.log('=' .repeat(60));

    try {
      await this.step1_AuthenticateUser();
      await this.step2_SubmitSkillAssessments();
      await this.step3_RunGapAnalysis();
      await this.step4_ViewResults();
      await this.step5_VerifyDataPersistence();
      
      this.printFinalReport();
      
    } catch (error) {
      this.addResult('WORKFLOW', 'ERROR', `Critical error: ${error.message}`, error);
      throw error;
    } finally {
      await prisma.$disconnect();
    }
  }

  private async step1_AuthenticateUser() {
    console.log('\n📋 STEP 1: User Authentication');
    
    try {
      // Find our test user
      const user = await prisma.user.findUnique({
        where: { email: '<EMAIL>' }
      });

      if (!user) {
        this.addResult('AUTH', 'FAIL', 'Test user not found');
        throw new Error('Test user not found');
      }

      this.userId = user.id;
      this.addResult('AUTH', 'PASS', `User authenticated successfully`, { userId: this.userId });

    } catch (error) {
      this.addResult('AUTH', 'ERROR', `Authentication failed: ${error.message}`);
      throw error;
    }
  }

  private async step2_SubmitSkillAssessments() {
    console.log('\n📋 STEP 2: Submit Skill Assessments');

    try {
      // First, create or find skills
      const skillsData = [
        { name: 'JavaScript', description: 'Programming language for web development', category: 'Programming' },
        { name: 'Python', description: 'Programming language for data science and web development', category: 'Programming' },
        { name: 'React', description: 'JavaScript library for building user interfaces', category: 'Frontend Framework' }
      ];

      const skills = [];
      for (const skillData of skillsData) {
        const skill = await prisma.skill.upsert({
          where: { name: skillData.name },
          update: {},
          create: skillData
        });
        skills.push(skill);
        this.addResult('SKILL_CREATE', 'PASS', `Skill ${skillData.name} ready`, { id: skill.id });
      }

      // Create skill assessments
      const assessments = [
        {
          skillId: skills[0].id, // JavaScript
          selfRating: 8,
          confidenceLevel: 7,
          notes: 'Strong in ES6+ and React',
          assessmentType: 'SELF_ASSESSMENT'
        },
        {
          skillId: skills[1].id, // Python
          selfRating: 6,
          confidenceLevel: 5,
          notes: 'Used for data analysis projects',
          assessmentType: 'SELF_ASSESSMENT'
        },
        {
          skillId: skills[2].id, // React
          selfRating: 7,
          confidenceLevel: 6,
          notes: 'Building modern web applications',
          assessmentType: 'SELF_ASSESSMENT'
        }
      ];

      const createdAssessments = [];

      for (let i = 0; i < assessments.length; i++) {
        const assessment = assessments[i];
        const skillName = skillsData[i].name;
        try {
          const created = await prisma.skillAssessment.create({
            data: {
              ...assessment,
              userId: this.userId,
            }
          });
          createdAssessments.push(created);
          this.addResult('ASSESSMENT_SUBMIT', 'PASS', `Submitted ${skillName} assessment`, { id: created.id });
        } catch (error) {
          this.addResult('ASSESSMENT_SUBMIT', 'FAIL', `Failed to submit ${skillName}: ${error.message}`);
        }
      }

      if (createdAssessments.length === 0) {
        throw new Error('No assessments were created successfully');
      }

      this.addResult('ASSESSMENT_BATCH', 'PASS', `Successfully submitted ${createdAssessments.length} assessments`);

    } catch (error) {
      this.addResult('ASSESSMENT_BATCH', 'ERROR', `Assessment submission failed: ${error.message}`);
      throw error;
    }
  }

  private async step3_RunGapAnalysis() {
    console.log('\n📋 STEP 3: Run Gap Analysis');
    
    try {
      // Get user's skill assessments
      const userAssessments = await prisma.skillAssessment.findMany({
        where: { userId: this.userId }
      });

      if (userAssessments.length === 0) {
        this.addResult('GAP_ANALYSIS_PREP', 'FAIL', 'No skill assessments found for gap analysis');
        throw new Error('No skill assessments found');
      }

      this.addResult('GAP_ANALYSIS_PREP', 'PASS', `Found ${userAssessments.length} assessments for analysis`);

      // Create gap analysis record
      const gapAnalysis = await prisma.skillGapAnalysis.create({
        data: {
          userId: this.userId,
          targetCareerPathName: 'Full Stack Developer',
          experienceLevel: 'INTERMEDIATE',
          timeframe: 'SIX_MONTHS',
          status: 'COMPLETED',
          completionPercentage: 100,
          analysisData: {
            analysisDate: new Date().toISOString(),
            methodology: 'AI_COMPREHENSIVE',
            confidence: 0.85
          },
          skillGaps: [
            {
              skillId: 'skill-1',
              skillName: 'Node.js',
              currentLevel: 3,
              targetLevel: 8,
              gapSize: 5,
              gapSeverity: 'HIGH',
              marketDemand: 'HIGH',
              learningResources: ['Node.js Documentation', 'Express.js Tutorial'],
              estimatedLearningTime: 40
            },
            {
              skillId: 'skill-2',
              skillName: 'TypeScript',
              currentLevel: 4,
              targetLevel: 7,
              gapSize: 3,
              gapSeverity: 'MEDIUM',
              marketDemand: 'HIGH',
              learningResources: ['TypeScript Handbook', 'Advanced TypeScript'],
              estimatedLearningTime: 25
            }
          ],
          learningPlan: {
            totalEstimatedHours: 65,
            milestones: [
              {
                month: 1,
                skills: ['Node.js Basics'],
                estimatedHours: 20
              },
              {
                month: 2,
                skills: ['TypeScript Fundamentals'],
                estimatedHours: 15
              },
              {
                month: 3,
                skills: ['Advanced Node.js', 'TypeScript Advanced'],
                estimatedHours: 30
              }
            ]
          },
          marketData: {
            careerReadiness: {
              currentScore: 65,
              targetScore: 85,
              improvementPotential: 20,
              timeToTarget: 3
            }
          },
          expiresAt: new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000), // 6 months
          lastUpdated: new Date()
        }
      });

      this.addResult('GAP_ANALYSIS_CREATE', 'PASS', 'Gap analysis created successfully', { 
        id: gapAnalysis.id,
        skillGaps: gapAnalysis.skillGaps,
        careerReadiness: gapAnalysis.careerReadiness
      });

    } catch (error) {
      this.addResult('GAP_ANALYSIS_CREATE', 'ERROR', `Gap analysis failed: ${error.message}`);
      throw error;
    }
  }

  private async step4_ViewResults() {
    console.log('\n📋 STEP 4: View Analysis Results');
    
    try {
      // Get the gap analysis results
      const gapAnalyses = await prisma.skillGapAnalysis.findMany({
        where: { userId: this.userId },
        orderBy: { createdAt: 'desc' }
      });

      if (gapAnalyses.length === 0) {
        this.addResult('RESULTS_VIEW', 'FAIL', 'No gap analysis results found');
        throw new Error('No gap analysis results found');
      }

      const latestAnalysis = gapAnalyses[0];
      
      this.addResult('RESULTS_VIEW', 'PASS', 'Gap analysis results retrieved successfully', {
        analysisId: latestAnalysis.id,
        targetCareer: latestAnalysis.targetCareerPathName,
        skillGapsCount: Array.isArray(latestAnalysis.skillGaps) ? latestAnalysis.skillGaps.length : 0,
        careerReadiness: latestAnalysis.careerReadiness,
        status: latestAnalysis.status
      });

      // Verify the overview data structure
      const overview = {
        careerReadiness: latestAnalysis.careerReadiness,
        skillGaps: latestAnalysis.skillGaps,
        learningPlan: latestAnalysis.learningPlan,
        targetCareer: latestAnalysis.targetCareerPathName,
        timeframe: latestAnalysis.timeframe
      };

      this.addResult('OVERVIEW_DATA', 'PASS', 'Overview data structure verified', overview);

    } catch (error) {
      this.addResult('RESULTS_VIEW', 'ERROR', `Results viewing failed: ${error.message}`);
      throw error;
    }
  }

  private async step5_VerifyDataPersistence() {
    console.log('\n📋 STEP 5: Verify Data Persistence');
    
    try {
      // Verify skill assessments are persisted
      const assessmentCount = await prisma.skillAssessment.count({
        where: { userId: this.userId }
      });

      // Verify gap analysis is persisted
      const gapAnalysisCount = await prisma.skillGapAnalysis.count({
        where: { userId: this.userId }
      });

      this.addResult('DATA_PERSISTENCE', 'PASS', 'Data persistence verified', {
        skillAssessments: assessmentCount,
        gapAnalyses: gapAnalysisCount
      });

      if (assessmentCount === 0 || gapAnalysisCount === 0) {
        this.addResult('DATA_INTEGRITY', 'FAIL', 'Data integrity check failed - missing records');
      } else {
        this.addResult('DATA_INTEGRITY', 'PASS', 'Data integrity check passed');
      }

    } catch (error) {
      this.addResult('DATA_PERSISTENCE', 'ERROR', `Data persistence check failed: ${error.message}`);
      throw error;
    }
  }

  private printFinalReport() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 COMPREHENSIVE TESTING FINAL REPORT');
    console.log('='.repeat(60));

    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const errors = this.results.filter(r => r.status === 'ERROR').length;

    console.log(`✅ PASSED: ${passed}`);
    console.log(`❌ FAILED: ${failed}`);
    console.log(`⚠️  ERRORS: ${errors}`);
    console.log(`📊 TOTAL: ${this.results.length}`);

    if (failed === 0 && errors === 0) {
      console.log('\n🎉 ALL TESTS PASSED - WORKFLOW IS FULLY FUNCTIONAL!');
    } else {
      console.log('\n⚠️  ISSUES FOUND - WORKFLOW NEEDS ATTENTION');
      
      console.log('\nFailed Tests:');
      this.results.filter(r => r.status === 'FAIL').forEach(r => {
        console.log(`  ❌ ${r.step}: ${r.message}`);
      });

      console.log('\nError Tests:');
      this.results.filter(r => r.status === 'ERROR').forEach(r => {
        console.log(`  ⚠️  ${r.step}: ${r.message}`);
      });
    }
  }
}

// Run the comprehensive test
const tester = new ComprehensiveWorkflowTester();
tester.testCompleteWorkflow()
  .then(() => {
    console.log('\n✅ Comprehensive workflow testing completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Comprehensive workflow testing failed:', error);
    process.exit(1);
  });
