import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function resetUsers() {
  try {
    console.log('🗑️  Deleting all existing users...');
    
    // Delete all users (this will cascade delete related records)
    const deletedUsers = await prisma.user.deleteMany({});
    console.log(`✅ Deleted ${deletedUsers.count} users`);
    
    console.log('\n👤 Creating new test user...');
    
    // Hash the password
    const hashedPassword = await bcrypt.hash('TestPassword123!', 12);
    
    // Create a new test user
    const newUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Test User',
        emailVerified: new Date(), // Mark as verified for testing
      },
    });
    
    console.log('✅ New test user created successfully:');
    console.log('📧 Email:', newUser.email);
    console.log('🔑 Password: TestPassword123!');
    console.log('🆔 User ID:', newUser.id);
    console.log('📅 Created:', newUser.createdAt);
    
  } catch (error) {
    console.error('❌ Error resetting users:', error);
  } finally {
    await prisma.$disconnect();
  }
}

resetUsers();
