import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function createRealTestUser() {
  const testEmail = '<EMAIL>';
  const testPassword = 'TestPassword123!';
  
  try {
    console.log('🧪 Creating real test user for comprehensive testing...');
    
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: testEmail }
    });
    
    if (existingUser) {
      console.log('✅ Test user already exists:', existingUser.id);
      return existingUser;
    }
    
    // Hash password
    const hashedPassword = await bcrypt.hash(testPassword, 12);
    
    // Create user
    const newUser = await prisma.user.create({
      data: {
        email: testEmail,
        password: hashedPassword,
        name: 'Real Tester',
        emailVerified: new Date(),
        failedLoginAttempts: 0,
      }
    });
    
    console.log('✅ Real test user created successfully');
    console.log(`   ID: ${newUser.id}`);
    console.log(`   Email: ${newUser.email}`);
    console.log(`   Password: ${testPassword}`);
    
    return newUser;
    
  } catch (error) {
    console.error('❌ Error creating real test user:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the creation
createRealTestUser()
  .then((user) => {
    console.log('\n🎉 Real test user ready for comprehensive testing');
    console.log('Use these credentials:');
    console.log(`Email: <EMAIL>`);
    console.log(`Password: TestPassword123!`);
  })
  .catch((error) => {
    console.error('❌ Failed to create real test user:', error);
    process.exit(1);
  });
