import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function debugAPICall() {
  console.log('🔍 DEBUGGING API CALL ISSUE');
  console.log('=' .repeat(50));

  try {
    // Check what the database actually contains
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: {
        skillAssessments: {
          include: {
            skill: {
              include: {
                marketData: {
                  where: { isActive: true },
                  orderBy: { dataDate: 'desc' },
                  take: 1,
                },
              },
            },
          },
          orderBy: {
            assessmentDate: 'desc',
          },
        }
      }
    });

    if (!user) {
      console.log('❌ User not found');
      return;
    }

    console.log('👤 USER FOUND:');
    console.log(`   Email: ${user.email}`);
    console.log(`   ID: ${user.id}`);
    console.log(`   Skill Assessments: ${user.skillAssessments.length}`);

    if (user.skillAssessments.length > 0) {
      console.log('\n📝 RAW ASSESSMENTS FROM DATABASE:');
      user.skillAssessments.forEach((assessment, index) => {
        console.log(`${index + 1}. ${assessment.skill.name}:`);
        console.log(`   ID: ${assessment.id}`);
        console.log(`   Skill ID: ${assessment.skillId}`);
        console.log(`   Self Rating: ${assessment.selfRating}`);
        console.log(`   Confidence: ${assessment.confidenceLevel}`);
        console.log(`   Assessment Date: ${assessment.assessmentDate}`);
        console.log(`   Is Active: ${assessment.isActive}`);
        console.log(`   Notes: ${assessment.notes || 'None'}`);
        console.log('');
      });

      // Simulate what the API should return
      console.log('\n🔄 SIMULATING API RESPONSE:');
      
      // Group by skill and get latest assessment for each (same logic as API)
      const latestAssessments = user.skillAssessments.reduce((acc, assessment) => {
        if (!acc[assessment.skillId] || assessment.assessmentDate > acc[assessment.skillId].assessmentDate) {
          acc[assessment.skillId] = assessment;
        }
        return acc;
      }, {} as Record<string, typeof user.skillAssessments[0]>);

      const assessmentData = Object.values(latestAssessments).map(assessment => {
        const progressTrend = assessment.selfRating >= 7 ? 'IMPROVING' :
                             assessment.selfRating >= 5 ? 'STABLE' : 'DECLINING';

        return {
          skillId: assessment.skillId,
          skillName: assessment.skill.name,
          currentRating: assessment.selfRating,
          confidenceLevel: assessment.confidenceLevel,
          lastAssessed: assessment.assessmentDate.toISOString(),
          progressTrend,
          marketDemand: assessment.skill.marketData[0]?.demandLevel || undefined,
        };
      });

      console.log('API Response Data:');
      console.log(JSON.stringify({
        success: true,
        data: {
          assessments: assessmentData,
          summary: {
            totalSkills: assessmentData.length,
            averageRating: assessmentData.reduce((sum, a) => sum + a.currentRating, 0) / assessmentData.length,
            averageConfidence: assessmentData.reduce((sum, a) => sum + a.confidenceLevel, 0) / assessmentData.length,
            lastAssessmentDate: new Date(Math.max(...assessmentData.map(a => new Date(a.lastAssessed).getTime()))).toISOString(),
            skillsNeedingAttention: assessmentData.filter(a => a.currentRating <= 5 || a.confidenceLevel <= 5).length,
          },
        },
      }, null, 2));

      console.log('\n🎯 WHAT FRONTEND SHOULD RECEIVE:');
      const frontendAssessments = assessmentData.map((assessment: any) => ({
        skillId: assessment.skillId,
        skillName: assessment.skillName,
        selfRating: assessment.currentRating,
        confidenceLevel: assessment.confidenceLevel,
        assessmentType: 'SELF_ASSESSMENT' as const,
        lastUsed: assessment.lastAssessed,
      }));

      console.log('Frontend Assessments Array:');
      console.log(JSON.stringify(frontendAssessments, null, 2));
      console.log(`\nFrontend should show: userAssessments.length = ${frontendAssessments.length}`);

    } else {
      console.log('\n❌ NO ASSESSMENTS FOUND IN DATABASE');
      console.log('This explains why the frontend shows userAssessments.length: 0');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

debugAPICall();
