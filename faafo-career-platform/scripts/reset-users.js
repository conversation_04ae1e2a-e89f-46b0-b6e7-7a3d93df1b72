const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function resetUsers() {
  try {
    console.log('🗑️  Deleting all existing data...');

    // Delete related records first to avoid foreign key constraints
    console.log('Deleting FreedomFund records...');
    await prisma.freedomFund.deleteMany({});

    console.log('Deleting SkillAssessment records...');
    await prisma.skillAssessment.deleteMany({});

    console.log('Deleting UserSkillProgress records...');
    await prisma.userSkillProgress.deleteMany({});

    console.log('Deleting Assessment records...');
    await prisma.assessment.deleteMany({});

    console.log('Deleting AssessmentResponse records...');
    await prisma.assessmentResponse.deleteMany({});

    console.log('Deleting other user-related records...');
    await prisma.userLearningProgress.deleteMany({});
    await prisma.resume.deleteMany({});
    await prisma.skillGapAnalysis.deleteMany({});

    // Now delete all users
    console.log('Deleting users...');
    const deletedUsers = await prisma.user.deleteMany({});
    console.log(`✅ Deleted ${deletedUsers.count} users`);
    
    console.log('\n👤 Creating new test user...');
    
    // Hash the password
    const hashedPassword = await bcrypt.hash('TestPassword123!', 12);
    
    // Create a new test user
    const newUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Test User',
        emailVerified: new Date(), // Mark as verified for testing
      },
    });
    
    console.log('✅ New test user created successfully:');
    console.log('📧 Email:', newUser.email);
    console.log('🔑 Password: TestPassword123!');
    console.log('🆔 User ID:', newUser.id);
    console.log('📅 Created:', newUser.createdAt);
    
  } catch (error) {
    console.error('❌ Error resetting users:', error);
  } finally {
    await prisma.$disconnect();
  }
}

resetUsers();
