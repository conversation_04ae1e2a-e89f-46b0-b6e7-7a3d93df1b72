import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function verifyUser() {
  try {
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });
    
    if (!user) {
      console.log('❌ User <EMAIL> not found');
      return;
    }
    
    const passwordMatch = await bcrypt.compare('TestPassword123!', user.password);
    
    console.log('📋 USER VERIFICATION:');
    console.log(`   Email: ${user.email}`);
    console.log(`   ID: ${user.id}`);
    console.log(`   Name: ${user.name}`);
    console.log(`   Password matches TestPassword123!: ${passwordMatch ? '✅ YES' : '❌ NO'}`);
    console.log(`   Created: ${user.createdAt}`);
    
    // Check if this user has assessments and gap analyses
    const assessmentCount = await prisma.skillAssessment.count({
      where: { userId: user.id }
    });
    
    const gapAnalysisCount = await prisma.skillGapAnalysis.count({
      where: { userId: user.id }
    });
    
    console.log(`   Skill Assessments: ${assessmentCount}`);
    console.log(`   Gap Analyses: ${gapAnalysisCount}`);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

verifyUser();
