import { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// Simulate the API call directly
async function testDirectAPI() {
  console.log('🧪 TESTING DIRECT API CALL');
  console.log('=' .repeat(40));

  try {
    // Get the user directly
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (!user) {
      console.log('❌ User not found');
      return;
    }

    const userId = user.id;
    console.log(`👤 User ID: ${userId}`);

    // Direct database query (bypassing EdgeCaseHandler)
    const assessments = await prisma.skillAssessment.findMany({
      where: {
        userId,
        isActive: true,
      },
      include: {
        skill: {
          include: {
            marketData: {
              where: { isActive: true },
              orderBy: { dataDate: 'desc' },
              take: 1,
            },
          },
        },
      },
      orderBy: {
        assessmentDate: 'desc',
      },
    });

    console.log(`📊 Found ${assessments.length} assessments`);

    // Group by skill and get latest assessment for each
    const latestAssessments = assessments.reduce((acc, assessment) => {
      if (!acc[assessment.skillId] || assessment.assessmentDate > acc[assessment.skillId].assessmentDate) {
        acc[assessment.skillId] = assessment;
      }
      return acc;
    }, {} as Record<string, typeof assessments[0]>);

    const assessmentData = Object.values(latestAssessments).map(assessment => {
      const progressTrend = assessment.selfRating >= 7 ? 'IMPROVING' :
                           assessment.selfRating >= 5 ? 'STABLE' : 'DECLINING';

      return {
        skillId: assessment.skillId,
        skillName: assessment.skill.name,
        currentRating: assessment.selfRating,
        confidenceLevel: assessment.confidenceLevel,
        lastAssessed: assessment.assessmentDate.toISOString(),
        progressTrend,
        marketDemand: assessment.skill.marketData[0]?.demandLevel || undefined,
      };
    });

    // Calculate summary
    const totalSkills = assessmentData.length;
    const averageRating = totalSkills > 0
      ? assessmentData.reduce((sum, a) => sum + a.currentRating, 0) / totalSkills
      : 0;
    const averageConfidence = totalSkills > 0
      ? assessmentData.reduce((sum, a) => sum + a.confidenceLevel, 0) / totalSkills
      : 0;
    const lastAssessmentDate = assessmentData.length > 0
      ? Math.max(...assessmentData.map(a => new Date(a.lastAssessed).getTime()))
      : Date.now();
    const skillsNeedingAttention = assessmentData.filter(a =>
      a.currentRating <= 5 || a.confidenceLevel <= 5
    ).length;

    const responseData = {
      success: true,
      data: {
        assessments: assessmentData,
        summary: {
          totalSkills,
          averageRating: Math.round(averageRating * 10) / 10,
          averageConfidence: Math.round(averageConfidence * 10) / 10,
          lastAssessmentDate: new Date(lastAssessmentDate).toISOString(),
          skillsNeedingAttention,
        },
      },
    };

    console.log('\n✅ API RESPONSE SIMULATION:');
    console.log(JSON.stringify(responseData, null, 2));

    console.log('\n🎯 FRONTEND CONVERSION:');
    const frontendAssessments = assessmentData.map((assessment: any) => ({
      skillId: assessment.skillId,
      skillName: assessment.skillName,
      selfRating: assessment.currentRating,
      confidenceLevel: assessment.confidenceLevel,
      assessmentType: 'SELF_ASSESSMENT' as const,
      lastUsed: assessment.lastAssessed,
    }));

    console.log(`Frontend should receive ${frontendAssessments.length} assessments`);
    console.log('First assessment:', frontendAssessments[0]);

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testDirectAPI();
