<!DOCTYPE html>
<html>
<head>
    <title>Force Logout</title>
    <script>
        async function forceLogout() {
            try {
                // Clear all localStorage
                localStorage.clear();
                console.log('✓ localStorage cleared');
                
                // Clear all sessionStorage
                sessionStorage.clear();
                console.log('✓ sessionStorage cleared');
                
                // Clear all cookies
                document.cookie.split(";").forEach(function(c) { 
                    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
                });
                console.log('✓ Cookies cleared');
                
                // Clear NextAuth specific cookies
                const authCookies = [
                    'next-auth.session-token',
                    'next-auth.csrf-token',
                    'next-auth.callback-url',
                    '__Secure-next-auth.session-token',
                    '__Host-next-auth.csrf-token'
                ];
                
                authCookies.forEach(cookieName => {
                    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=localhost;`;
                    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
                });
                console.log('✓ NextAuth cookies cleared');
                
                // Clear IndexedDB
                if ('indexedDB' in window) {
                    try {
                        const databases = await indexedDB.databases();
                        await Promise.all(databases.map(db => {
                            return new Promise((resolve, reject) => {
                                const deleteReq = indexedDB.deleteDatabase(db.name);
                                deleteReq.onsuccess = () => resolve();
                                deleteReq.onerror = () => reject(deleteReq.error);
                            });
                        }));
                        console.log('✓ IndexedDB cleared');
                    } catch (e) {
                        console.log('⚠ IndexedDB clear failed:', e);
                    }
                }
                
                // Call NextAuth signout API
                try {
                    await fetch('/api/auth/signout', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({})
                    });
                    console.log('✓ NextAuth signout API called');
                } catch (e) {
                    console.log('⚠ NextAuth signout API failed:', e);
                }
                
                // Clear any auth state in memory
                if (window.localStorage) {
                    window.localStorage.removeItem('auth_state');
                    window.localStorage.removeItem('auth_state_change');
                }
                
                document.getElementById('status').innerHTML = `
                    <h2 style="color: green;">✓ Force Logout Complete!</h2>
                    <p>All authentication data has been cleared.</p>
                    <p><strong>Next steps:</strong></p>
                    <ol>
                        <li>Close all browser tabs for this application</li>
                        <li>Clear your browser cache (Ctrl+Shift+Delete / Cmd+Shift+Delete)</li>
                        <li>Restart your browser</li>
                        <li>Navigate to <a href="http://localhost:3000">http://localhost:3000</a></li>
                    </ol>
                `;
                
                // Auto-redirect after 3 seconds
                setTimeout(() => {
                    window.location.href = 'http://localhost:3000';
                }, 3000);
                
            } catch (error) {
                console.error('Force logout error:', error);
                document.getElementById('status').innerHTML = `
                    <h2 style="color: red;">❌ Error during logout</h2>
                    <p>Error: ${error.message}</p>
                    <p>Please manually clear your browser data and restart the browser.</p>
                `;
            }
        }
        
        // Auto-run on page load
        window.onload = forceLogout;
    </script>
</head>
<body>
    <h1>Force Logout in Progress...</h1>
    <div id="status">
        <p>Clearing all authentication data...</p>
        <p>Please wait...</p>
    </div>
</body>
</html>
