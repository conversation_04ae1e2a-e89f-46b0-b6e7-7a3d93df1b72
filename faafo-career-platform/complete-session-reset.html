<!DOCTYPE html>
<html>
<head>
    <title>Complete Session Reset</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        button { padding: 10px 20px; margin: 10px 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Complete Session Reset</h1>
    <p>This will completely clear all authentication data and reset the session.</p>
    
    <div id="status"></div>
    
    <button onclick="performCompleteReset()">Start Complete Reset</button>
    <button onclick="manualSteps()">Show Manual Steps</button>
    
    <script>
        async function performCompleteReset() {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = '<div class="step">Starting complete session reset...</div>';
            
            const steps = [];
            
            try {
                // Step 1: Clear all storage
                steps.push('Clearing localStorage...');
                localStorage.clear();
                
                steps.push('Clearing sessionStorage...');
                sessionStorage.clear();
                
                // Step 2: Clear all cookies aggressively
                steps.push('Clearing all cookies...');
                const cookies = document.cookie.split(";");
                for (let cookie of cookies) {
                    const eqPos = cookie.indexOf("=");
                    const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
                    
                    // Clear for multiple domains and paths
                    const domains = ['localhost', '.localhost', '127.0.0.1', '.127.0.0.1'];
                    const paths = ['/', '/api', '/api/auth'];
                    
                    for (let domain of domains) {
                        for (let path of paths) {
                            document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path}; domain=${domain};`;
                            document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path};`;
                        }
                    }
                }
                
                // Step 3: Clear NextAuth specific cookies
                steps.push('Clearing NextAuth cookies...');
                const nextAuthCookies = [
                    'next-auth.session-token',
                    'next-auth.csrf-token',
                    'next-auth.callback-url',
                    '__Secure-next-auth.session-token',
                    '__Host-next-auth.csrf-token',
                    'next-auth.pkce.code_verifier'
                ];
                
                for (let cookieName of nextAuthCookies) {
                    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
                    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=localhost;`;
                    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/api/auth;`;
                }
                
                // Step 4: Call NextAuth signout multiple times
                steps.push('Calling NextAuth signout API...');
                try {
                    await fetch('/api/auth/signout', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({})
                    });
                } catch (e) {
                    console.log('NextAuth signout failed:', e);
                }
                
                // Step 5: Clear IndexedDB
                steps.push('Clearing IndexedDB...');
                if ('indexedDB' in window) {
                    try {
                        const databases = await indexedDB.databases();
                        await Promise.all(databases.map(db => {
                            return new Promise((resolve) => {
                                const deleteReq = indexedDB.deleteDatabase(db.name);
                                deleteReq.onsuccess = () => resolve();
                                deleteReq.onerror = () => resolve(); // Continue even if fails
                            });
                        }));
                    } catch (e) {
                        console.log('IndexedDB clear failed:', e);
                    }
                }
                
                // Step 6: Clear any auth state in memory
                steps.push('Clearing auth state...');
                if (window.localStorage) {
                    const authKeys = [
                        'auth_state',
                        'auth_state_change',
                        'nextauth.message',
                        'nextauth.session',
                        'user_session',
                        'session_data'
                    ];
                    authKeys.forEach(key => {
                        window.localStorage.removeItem(key);
                        window.sessionStorage.removeItem(key);
                    });
                }
                
                // Step 7: Force reload to clear memory
                steps.push('Session reset complete!');
                
                statusDiv.innerHTML = steps.map(step => 
                    `<div class="step success">✓ ${step}</div>`
                ).join('');
                
                statusDiv.innerHTML += `
                    <div class="step warning">
                        <h3>⚠️ Important Next Steps:</h3>
                        <ol>
                            <li><strong>Close ALL browser tabs</strong> for localhost:3000</li>
                            <li><strong>Clear browser cache:</strong>
                                <ul>
                                    <li>Chrome/Edge: Ctrl+Shift+Delete (Cmd+Shift+Delete on Mac)</li>
                                    <li>Firefox: Ctrl+Shift+Delete (Cmd+Shift+Delete on Mac)</li>
                                    <li>Safari: Cmd+Option+E</li>
                                </ul>
                            </li>
                            <li><strong>Restart your browser completely</strong></li>
                            <li><strong>Navigate to <a href="http://localhost:3000" target="_blank">http://localhost:3000</a></strong></li>
                        </ol>
                        <p><strong>Auto-redirect in 5 seconds...</strong></p>
                    </div>
                `;
                
                // Auto-redirect after 5 seconds
                setTimeout(() => {
                    window.location.href = 'http://localhost:3000';
                }, 5000);
                
            } catch (error) {
                statusDiv.innerHTML += `<div class="step error">❌ Error: ${error.message}</div>`;
            }
        }
        
        function manualSteps() {
            document.getElementById('status').innerHTML = `
                <div class="step warning">
                    <h3>Manual Session Reset Steps:</h3>
                    <ol>
                        <li><strong>Open Developer Tools</strong> (F12)</li>
                        <li><strong>Go to Application/Storage tab</strong></li>
                        <li><strong>Clear Storage:</strong>
                            <ul>
                                <li>Local Storage → Clear All</li>
                                <li>Session Storage → Clear All</li>
                                <li>Cookies → Delete All for localhost</li>
                                <li>IndexedDB → Delete All</li>
                            </ul>
                        </li>
                        <li><strong>Go to Network tab</strong></li>
                        <li><strong>Check "Disable cache"</strong></li>
                        <li><strong>Hard refresh:</strong> Ctrl+Shift+R (Cmd+Shift+R on Mac)</li>
                        <li><strong>Navigate to:</strong> <a href="http://localhost:3000">http://localhost:3000</a></li>
                    </ol>
                </div>
            `;
        }
        
        // Auto-run on page load
        window.onload = () => {
            document.getElementById('status').innerHTML = `
                <div class="step">
                    <p>Ready to perform complete session reset.</p>
                    <p>This will clear all authentication data and force a fresh start.</p>
                </div>
            `;
        };
    </script>
</body>
</html>
