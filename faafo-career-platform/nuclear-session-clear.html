<!DOCTYPE html>
<html>
<head>
    <title>NUCLEAR SESSION CLEAR</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #000; color: #fff; }
        .warning { background: #ff0000; color: #fff; padding: 20px; margin: 20px 0; border-radius: 5px; }
        button { padding: 15px 30px; margin: 10px; cursor: pointer; font-size: 16px; }
        .nuke { background: #ff0000; color: #fff; border: none; }
    </style>
</head>
<body>
    <h1>🚨 NUCLEAR SESSION CLEAR 🚨</h1>
    <div class="warning">
        <h2>⚠️ WARNING: NUCLEAR OPTION ⚠️</h2>
        <p>This will COMPLETELY DESTROY all session data, cookies, and storage.</p>
        <p>Use this when the session is being extremely stubborn.</p>
    </div>
    
    <button class="nuke" onclick="nuclearClear()">🚀 NUKE ALL SESSIONS</button>
    
    <div id="status"></div>
    
    <script>
        async function nuclearClear() {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = '<h2>🚀 NUCLEAR CLEARING IN PROGRESS...</h2>';
            
            try {
                // Step 1: Clear ALL storage
                console.log('🧹 Clearing all storage...');
                localStorage.clear();
                sessionStorage.clear();
                
                // Step 2: NUKE all cookies
                console.log('💣 NUKING all cookies...');
                const cookies = document.cookie.split(";");
                for (let cookie of cookies) {
                    const eqPos = cookie.indexOf("=");
                    const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
                    
                    // Multiple domain/path combinations
                    const domains = ['localhost', '.localhost', '127.0.0.1', '.127.0.0.1', ''];
                    const paths = ['/', '/api', '/api/auth', ''];
                    
                    for (let domain of domains) {
                        for (let path of paths) {
                            if (domain) {
                                document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path}; domain=${domain}; secure=false; samesite=lax;`;
                            } else {
                                document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path}; secure=false; samesite=lax;`;
                            }
                        }
                    }
                }
                
                // Step 3: DESTROY NextAuth cookies specifically
                console.log('🎯 Targeting NextAuth cookies...');
                const nextAuthCookies = [
                    'next-auth.session-token',
                    'next-auth.csrf-token', 
                    'next-auth.callback-url',
                    '__Secure-next-auth.session-token',
                    '__Host-next-auth.csrf-token',
                    'authjs.session-token',
                    'authjs.csrf-token',
                    'next-auth.pkce.code_verifier'
                ];
                
                for (let cookieName of nextAuthCookies) {
                    // Multiple attempts with different configurations
                    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
                    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=localhost;`;
                    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/api/auth;`;
                    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/api/auth; domain=localhost;`;
                    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; secure=false; samesite=lax;`;
                }
                
                // Step 4: Clear IndexedDB
                console.log('🗄️ Clearing IndexedDB...');
                if ('indexedDB' in window) {
                    try {
                        const databases = await indexedDB.databases();
                        await Promise.all(databases.map(db => {
                            return new Promise((resolve) => {
                                const deleteReq = indexedDB.deleteDatabase(db.name);
                                deleteReq.onsuccess = () => resolve();
                                deleteReq.onerror = () => resolve();
                            });
                        }));
                    } catch (e) {
                        console.log('IndexedDB clear failed:', e);
                    }
                }
                
                // Step 5: Call multiple logout APIs
                console.log('📞 Calling logout APIs...');
                const logoutUrls = [
                    '/api/auth/signout',
                    '/api/auth/force-logout',
                    '/api/auth/clear-all-sessions'
                ];
                
                for (let url of logoutUrls) {
                    try {
                        await fetch(url, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({})
                        });
                        console.log(`✅ Called ${url}`);
                    } catch (e) {
                        console.log(`❌ Failed ${url}:`, e);
                    }
                }
                
                // Step 6: Clear any remaining auth state
                console.log('🧽 Final cleanup...');
                const authKeys = [
                    'auth_state', 'auth_state_change', 'nextauth.message',
                    'nextauth.session', 'user_session', 'session_data',
                    'authjs.session', 'authjs.csrf-token'
                ];
                authKeys.forEach(key => {
                    try {
                        localStorage.removeItem(key);
                        sessionStorage.removeItem(key);
                    } catch (e) {}
                });
                
                statusDiv.innerHTML = `
                    <div style="background: #00ff00; color: #000; padding: 20px; border-radius: 5px;">
                        <h2>💥 NUCLEAR CLEAR COMPLETE! 💥</h2>
                        <p><strong>ALL SESSION DATA HAS BEEN OBLITERATED</strong></p>
                        <p>🔥 Cookies: DESTROYED</p>
                        <p>🔥 Storage: WIPED</p>
                        <p>🔥 IndexedDB: ANNIHILATED</p>
                        <p>🔥 Auth APIs: CALLED</p>
                        
                        <h3>🚨 MANDATORY NEXT STEPS:</h3>
                        <ol>
                            <li><strong>CLOSE ALL BROWSER TABS</strong> for localhost</li>
                            <li><strong>RESTART YOUR BROWSER COMPLETELY</strong></li>
                            <li><strong>Clear browser cache</strong> (Ctrl+Shift+Delete)</li>
                            <li><strong>Navigate to localhost:3001</strong></li>
                        </ol>
                        
                        <p><strong>Auto-redirect in 5 seconds...</strong></p>
                    </div>
                `;
                
                // Force redirect
                setTimeout(() => {
                    window.location.href = 'http://localhost:3001';
                }, 5000);
                
            } catch (error) {
                statusDiv.innerHTML = `
                    <div style="background: #ff0000; padding: 20px; border-radius: 5px;">
                        <h2>💀 NUCLEAR CLEAR FAILED</h2>
                        <p>Error: ${error.message}</p>
                        <p><strong>MANUAL INTERVENTION REQUIRED:</strong></p>
                        <ol>
                            <li>Open DevTools (F12)</li>
                            <li>Go to Application tab</li>
                            <li>Clear ALL storage manually</li>
                            <li>Restart browser</li>
                        </ol>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
