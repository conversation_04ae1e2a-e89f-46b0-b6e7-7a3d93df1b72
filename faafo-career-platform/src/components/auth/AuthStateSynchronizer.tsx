'use client';

import React, { useEffect, useRef, useState } from 'react';
import { useSession } from 'next-auth/react';
import { getAuthStateManager } from '@/lib/auth-state-manager';

/**
 * AuthStateSynchronizer Component
 * 
 * This component ensures that authentication state is synchronized across
 * all components by listening to session changes and updating the global
 * auth state manager accordingly.
 * 
 * It should be placed high in the component tree (ideally in the root layout)
 * to ensure all child components receive consistent authentication state.
 */
export function AuthStateSynchronizer({ children }: { children: React.ReactNode }) {
  const { data: session, status } = useSession();
  const authStateManager = getAuthStateManager();
  const lastStatusRef = useRef<string>('');
  const lastSessionIdRef = useRef<string>('');
  const [mounted, setMounted] = useState(false);

  // Prevent hydration mismatch by only running client-side effects after mount
  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    // Get current session identifier for comparison
    const currentSessionId = session?.user?.id || '';
    const statusChanged = lastStatusRef.current !== status;
    const sessionChanged = lastSessionIdRef.current !== currentSessionId;

    // Only update if there's an actual change to prevent unnecessary re-renders
    if (statusChanged || sessionChanged) {
      console.log('AuthStateSynchronizer: Session state changed', {
        status,
        userId: session?.user?.id,
        statusChanged,
        sessionChanged
      });

      // Update the auth state manager with the latest session data
      authStateManager.refreshState().catch(error => {
        console.error('AuthStateSynchronizer: Failed to refresh auth state', error);
      });

      // Update refs for next comparison
      lastStatusRef.current = status;
      lastSessionIdRef.current = currentSessionId;
    }
  }, [session, status, authStateManager]);

  // Set up listener for auth state changes to ensure components stay in sync
  useEffect(() => {
    let logCount = 0;
    const maxLogs = 5; // Limit logging to prevent spam

    const unsubscribe = authStateManager.addStateChangeListener((newState) => {
      // Only log first few state changes to prevent spam
      if (logCount < maxLogs) {
        console.log('AuthStateSynchronizer: Auth state updated', newState);
        logCount++;
      } else if (logCount === maxLogs) {
        console.log('AuthStateSynchronizer: Further auth state updates will not be logged to prevent spam');
        logCount++;
      }
    });

    return () => {
      unsubscribe();
    };
  }, [authStateManager]);

  // Set up cross-tab synchronization (only after mount)
  useEffect(() => {
    if (!mounted) return;

    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === 'auth_state_change') {
        console.log('AuthStateSynchronizer: Cross-tab auth state change detected');
        authStateManager.refreshState().catch(error => {
          console.error('AuthStateSynchronizer: Failed to refresh auth state from storage event', error);
        });
      }
    };

    // Listen for storage events from other tabs
    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [authStateManager, mounted]);

  // Set up periodic session validation (reduced frequency, only after mount)
  useEffect(() => {
    if (!mounted) return;

    const interval = setInterval(() => {
      const currentState = authStateManager.getState();
      if (currentState.isAuthenticated && !currentState.isLoading) {
        authStateManager.validateSession().catch(error => {
          console.error('AuthStateSynchronizer: Periodic session validation failed', error);
        });
      }
    }, 15 * 60 * 1000); // Check every 15 minutes (reduced from 5 minutes)

    return () => {
      clearInterval(interval);
    };
  }, [authStateManager, mounted]);

  // Force a refresh when the component mounts to ensure initial state is correct (only after mount)
  useEffect(() => {
    if (!mounted) return;

    authStateManager.refreshState().catch(error => {
      console.error('AuthStateSynchronizer: Failed to refresh auth state on mount', error);
    });
  }, [authStateManager, mounted]);

  return <>{children}</>;
}

/**
 * Hook to force authentication state refresh
 * Useful for components that need to manually trigger a state refresh
 */
export function useAuthStateRefresh() {
  const authStateManager = getAuthStateManager();

  const refreshAuthState = React.useCallback(async () => {
    try {
      await authStateManager.refreshState();
      console.log('AuthStateRefresh: Manual refresh completed');
    } catch (error) {
      console.error('AuthStateRefresh: Manual refresh failed', error);
      throw error;
    }
  }, [authStateManager]);

  return { refreshAuthState };
}

/**
 * Component that provides authentication state debugging information
 * Only renders in development mode
 */
export function AuthStateDebugger() {
  const { data: session, status } = useSession();
  const authStateManager = getAuthStateManager();
  const authState = authStateManager.getState();

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black/80 text-white p-3 rounded-lg text-xs max-w-sm z-50">
      <div className="font-bold mb-2">Auth State Debug</div>
      <div>Session Status: {status}</div>
      <div>Session User ID: {session?.user?.id || 'None'}</div>
      <div>Auth State Authenticated: {authState.isAuthenticated ? 'Yes' : 'No'}</div>
      <div>Auth State Loading: {authState.isLoading ? 'Yes' : 'No'}</div>
      <div>Auth State User ID: {authState.user?.id || 'None'}</div>
      <div>Auth State Admin: {authState.isAdmin ? 'Yes' : 'No'}</div>
      {authState.error && <div className="text-red-300">Error: {authState.error}</div>}
    </div>
  );
}
