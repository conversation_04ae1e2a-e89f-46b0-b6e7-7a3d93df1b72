'use client';

import { useEffect } from 'react';
import Head from 'next/head';
import { generateSkillGapSEO, SkillGapSEOData } from '@/lib/seo/skill-gap-seo';

interface SkillGapSEOProps {
  page: 'main' | 'assessment' | 'analysis' | 'results';
  data?: SkillGapSEOData;
  children?: React.ReactNode;
}

export default function SkillGapSEO({ page, data, children }: SkillGapSEOProps) {
  const seoData = generateSkillGapSEO(page, data);

  useEffect(() => {
    // Update document title dynamically
    if (seoData.title) {
      document.title = typeof seoData.title === 'string' ? seoData.title : seoData.title.default || '';
    }

    // Update meta description
    if (seoData.description) {
      const metaDescription = document.querySelector('meta[name="description"]');
      if (metaDescription) {
        metaDescription.setAttribute('content', seoData.description);
      } else {
        const meta = document.createElement('meta');
        meta.name = 'description';
        meta.content = seoData.description;
        document.head.appendChild(meta);
      }
    }

    // Update Open Graph tags
    if (seoData.openGraph) {
      const updateOGTag = (property: string, content: string) => {
        let ogTag = document.querySelector(`meta[property="${property}"]`);
        if (ogTag) {
          ogTag.setAttribute('content', content);
        } else {
          ogTag = document.createElement('meta');
          ogTag.setAttribute('property', property);
          ogTag.setAttribute('content', content);
          document.head.appendChild(ogTag);
        }
      };

      if (seoData.openGraph.title) {
        updateOGTag('og:title', seoData.openGraph.title);
      }
      if (seoData.openGraph.description) {
        updateOGTag('og:description', seoData.openGraph.description);
      }
      if (seoData.openGraph.url) {
        updateOGTag('og:url', seoData.openGraph.url);
      }
      if (seoData.openGraph.type) {
        updateOGTag('og:type', seoData.openGraph.type);
      }
    }

    // Update Twitter Card tags
    if (seoData.twitter) {
      const updateTwitterTag = (name: string, content: string) => {
        let twitterTag = document.querySelector(`meta[name="${name}"]`);
        if (twitterTag) {
          twitterTag.setAttribute('content', content);
        } else {
          twitterTag = document.createElement('meta');
          twitterTag.setAttribute('name', name);
          twitterTag.setAttribute('content', content);
          document.head.appendChild(twitterTag);
        }
      };

      if (seoData.twitter.card) {
        updateTwitterTag('twitter:card', seoData.twitter.card);
      }
      if (seoData.twitter.title) {
        updateTwitterTag('twitter:title', seoData.twitter.title);
      }
      if (seoData.twitter.description) {
        updateTwitterTag('twitter:description', seoData.twitter.description);
      }
    }

    // Update canonical URL
    if (seoData.alternates?.canonical) {
      let canonicalLink = document.querySelector('link[rel="canonical"]');
      if (canonicalLink) {
        canonicalLink.setAttribute('href', seoData.alternates.canonical);
      } else {
        canonicalLink = document.createElement('link');
        canonicalLink.setAttribute('rel', 'canonical');
        canonicalLink.setAttribute('href', seoData.alternates.canonical);
        document.head.appendChild(canonicalLink);
      }
    }
  }, [page, data, seoData]);

  return (
    <>
      <Head>
        {/* Keywords meta tag */}
        {seoData.keywords && (
          <meta name="keywords" content={seoData.keywords} />
        )}
        
        {/* Robots meta tag */}
        {seoData.robots && (
          <meta 
            name="robots" 
            content={`${seoData.robots.index ? 'index' : 'noindex'}, ${seoData.robots.follow ? 'follow' : 'nofollow'}`} 
          />
        )}

        {/* Additional meta tags for skill gap analyzer */}
        <meta name="application-name" content="FAAFO Skill Gap Analyzer" />
        <meta name="theme-color" content="#3b82f6" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-title" content="Skill Gap Analyzer" />
        
        {/* Preconnect to external domains for performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        
        {/* DNS prefetch for potential external resources */}
        <link rel="dns-prefetch" href="//www.google-analytics.com" />
        <link rel="dns-prefetch" href="//vercel.live" />
      </Head>
      {children}
    </>
  );
}

/**
 * Hook for updating SEO data dynamically
 */
export function useSkillGapSEO(page: 'main' | 'assessment' | 'analysis' | 'results', data?: SkillGapSEOData) {
  useEffect(() => {
    const seoData = generateSkillGapSEO(page, data);
    
    // Update page title
    if (seoData.title) {
      document.title = typeof seoData.title === 'string' ? seoData.title : seoData.title.default || '';
    }

    // Track page view for analytics
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('config', process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID, {
        page_title: document.title,
        page_location: window.location.href,
        custom_map: {
          custom_parameter_1: page,
          custom_parameter_2: data?.careerPath || 'unknown',
          custom_parameter_3: data?.skillCount?.toString() || '0',
        }
      });
    }
  }, [page, data]);
}
