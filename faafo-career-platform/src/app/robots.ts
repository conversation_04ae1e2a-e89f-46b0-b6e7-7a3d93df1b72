import { MetadataRoute } from 'next';

export default function robots(): MetadataRoute.Robots {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://faafo.com';
  
  return {
    rules: [
      {
        userAgent: '*',
        allow: [
          '/',
          '/skills/',
          '/skills/gap-analyzer',
          '/career-paths/',
          '/learning-resources/',
          '/tools/',
          '/about',
          '/contact',
          '/privacy',
          '/terms',
          '/blog/',
          '/guides/',
        ],
        disallow: [
          '/api/',
          '/dashboard/',
          '/progress/',
          '/interview-practice/',
          '/resume-builder/',
          '/salary-calculator/',
          '/admin/',
          '/auth/',
          '/_next/',
          '/private/',
          '*.json',
          '/skills/gap-analyzer?tab=results*', // User-specific results
          '/skills/gap-analyzer?analysis=*',   // Specific analysis results
        ],
      },
      {
        userAgent: 'GPTBot',
        disallow: ['/'],
      },
      {
        userAgent: 'ChatGPT-User',
        disallow: ['/'],
      },
      {
        userAgent: 'CCBot',
        disallow: ['/'],
      },
      {
        userAgent: 'anthropic-ai',
        disallow: ['/'],
      },
      {
        userAgent: 'Claude-Web',
        disallow: ['/'],
      },
    ],
    sitemap: `${baseUrl}/sitemap.xml`,
    host: baseUrl,
  };
}
