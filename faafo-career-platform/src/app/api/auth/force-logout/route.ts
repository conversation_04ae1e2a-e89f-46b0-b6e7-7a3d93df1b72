import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

/**
 * Force logout endpoint that aggressively clears all session data
 * This is useful for development when sessions get stuck
 */
export async function POST(request: NextRequest) {
  try {
    console.log('Force logout requested');
    
    // Get all cookies
    const cookieStore = cookies();
    const allCookies = cookieStore.getAll();
    
    // Create response with cleared cookies
    const response = NextResponse.json({ 
      success: true, 
      message: 'Force logout completed',
      clearedCookies: allCookies.length
    });
    
    // Clear all NextAuth related cookies
    const nextAuthCookies = [
      'next-auth.session-token',
      'next-auth.csrf-token',
      'next-auth.callback-url',
      '__Secure-next-auth.session-token',
      '__Host-next-auth.csrf-token',
      'next-auth.pkce.code_verifier'
    ];
    
    // Clear NextAuth cookies
    nextAuthCookies.forEach(cookieName => {
      response.cookies.set(cookieName, '', {
        expires: new Date(0),
        path: '/',
        httpOnly: true,
        secure: false, // Set to false for localhost
        sameSite: 'lax'
      });
      
      response.cookies.set(cookieName, '', {
        expires: new Date(0),
        path: '/api/auth',
        httpOnly: true,
        secure: false,
        sameSite: 'lax'
      });
    });
    
    // Clear all existing cookies
    allCookies.forEach(cookie => {
      response.cookies.set(cookie.name, '', {
        expires: new Date(0),
        path: '/',
        httpOnly: true,
        secure: false,
        sameSite: 'lax'
      });
    });
    
    console.log(`Force logout completed, cleared ${allCookies.length} cookies`);
    
    return response;
    
  } catch (error) {
    console.error('Force logout error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to force logout',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'Use POST method to force logout',
    endpoint: '/api/auth/force-logout',
    method: 'POST'
  });
}
