import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { cookies } from 'next/headers';

/**
 * Clear all sessions endpoint - removes all session data from database and cookies
 * This is a nuclear option for development when sessions get completely stuck
 */
export async function POST(request: NextRequest) {
  try {
    console.log('Clear all sessions requested');
    
    // Clear all NextAuth sessions from database
    const deletedSessions = await prisma.session.deleteMany({});
    console.log(`Deleted ${deletedSessions.count} sessions from database`);
    
    // Clear all NextAuth accounts (optional - be careful with this)
    // const deletedAccounts = await prisma.account.deleteMany({});
    // console.log(`Deleted ${deletedAccounts.count} accounts from database`);
    
    // Clear all verification tokens
    const deletedTokens = await prisma.verificationToken.deleteMany({});
    console.log(`Deleted ${deletedTokens.count} verification tokens from database`);
    
    // Get all cookies
    const cookieStore = cookies();
    const allCookies = cookieStore.getAll();
    
    // Create response with cleared cookies
    const response = NextResponse.json({ 
      success: true, 
      message: 'All sessions cleared from database and cookies',
      deletedSessions: deletedSessions.count,
      deletedTokens: deletedTokens.count,
      clearedCookies: allCookies.length
    });
    
    // Clear all NextAuth related cookies
    const nextAuthCookies = [
      'next-auth.session-token',
      'next-auth.csrf-token',
      'next-auth.callback-url',
      '__Secure-next-auth.session-token',
      '__Host-next-auth.csrf-token',
      'next-auth.pkce.code_verifier',
      'authjs.session-token',
      'authjs.csrf-token',
      'authjs.callback-url'
    ];
    
    // Clear NextAuth cookies
    nextAuthCookies.forEach(cookieName => {
      response.cookies.set(cookieName, '', {
        expires: new Date(0),
        path: '/',
        httpOnly: true,
        secure: false, // Set to false for localhost
        sameSite: 'lax'
      });
      
      response.cookies.set(cookieName, '', {
        expires: new Date(0),
        path: '/api/auth',
        httpOnly: true,
        secure: false,
        sameSite: 'lax'
      });
    });
    
    // Clear all existing cookies
    allCookies.forEach(cookie => {
      response.cookies.set(cookie.name, '', {
        expires: new Date(0),
        path: '/',
        httpOnly: true,
        secure: false,
        sameSite: 'lax'
      });
    });
    
    console.log(`Clear all sessions completed successfully`);
    
    return response;
    
  } catch (error) {
    console.error('Clear all sessions error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to clear all sessions',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get session count for information
    const sessionCount = await prisma.session.count();
    const tokenCount = await prisma.verificationToken.count();
    
    return NextResponse.json({
      message: 'Use POST method to clear all sessions',
      endpoint: '/api/auth/clear-all-sessions',
      method: 'POST',
      currentSessions: sessionCount,
      currentTokens: tokenCount
    });
  } catch (error) {
    return NextResponse.json({
      error: 'Failed to get session info',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
