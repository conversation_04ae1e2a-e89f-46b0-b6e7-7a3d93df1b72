import { NextRequest } from 'next/server';
import prisma from '@/lib/prisma';
import { sendPasswordResetEmail } from '@/lib/email';
import { v4 as uuidv4 } from 'uuid';
import bcrypt from 'bcrypt';
import { validateInput, emailSchema } from '@/lib/validation';
import { createSuccessResponse, ErrorResponses, withErrorHandling } from '@/lib/api-response';
import { withRateLimit, rateLimiters } from '@/lib/rate-limit';
import { CONFIG } from '@/lib/config';

export const POST = withErrorHandling(async (request: NextRequest) => {
  // Apply rate limiting
  const rateLimitResult = withRateLimit(rateLimiters.passwordReset)(request);
  if (!rateLimitResult.allowed) {
    return ErrorResponses.tooManyRequests();
  }

  const body = await request.json();

  // Validate email
  const validation = validateInput(emailSchema, body.email);
  if (!validation.success) {
    return ErrorResponses.validationError([validation.error]);
  }

  const { email } = validation.data;

  const user = await prisma.user.findUnique({ where: { email } });

  if (!user) {
    // For security reasons, don't reveal if the user exists or not.
    return createSuccessResponse(
      null,
      'If an account with that email exists, a password reset link has been sent.'
    );
  }

  const resetToken = uuidv4();
  const passwordResetToken = await bcrypt.hash(resetToken, CONFIG.SECURITY.BCRYPT_ROUNDS);
  const passwordResetExpires = new Date(Date.now() + CONFIG.AUTH.PASSWORD_RESET_EXPIRY_MS);

  await prisma.user.update({
    where: { id: user.id },
    data: {
      passwordResetToken,
      passwordResetExpires,
    },
  });

  const resetUrl = `${process.env.NEXTAUTH_URL}/auth/reset-password?token=${resetToken}`;

  await sendPasswordResetEmail({ to: email, url: resetUrl });

  return createSuccessResponse(
    null,
    'If an account with that email exists, a password reset link has been sent.'
  );
});