import { NextRequest, NextResponse } from "next/server";
import bcrypt from "bcrypt";
import { v4 as uuidv4 } from 'uuid';
import React from 'react';
import prisma from '@/lib/prisma';
import { sendEmail } from '@/lib/email';
import { VerificationEmail } from '@/emails/VerificationEmail';
import { withSimpleSecurity, SimpleSecurity, validateFormData } from '@/lib/simple-security';
import { ValidationPipelines } from '@/lib/validation-pipeline';

export const POST = withSimpleSecurity(
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Enhanced validation with comprehensive security pipeline
      const userPipeline = ValidationPipelines.createUserPipeline();
      const validationResult = await userPipeline.validate(body);

      if (!validationResult.isValid) {
        return NextResponse.json({
          message: "Invalid input data",
          details: validationResult.errors
        }, { status: 400 });
      }

      const { email, password } = validationResult.sanitizedData;

      // Additional legacy validation for compatibility
      const validation = validateFormData(body);
      if (!validation.isValid) {
        return NextResponse.json({
          message: "Invalid input data",
          details: validation.errors.join(', ')
        }, { status: 400 });
      }

      // Additional email validation
      const emailValidation = SimpleSecurity.validateEmail(email);
      if (!emailValidation.isValid) {
        return NextResponse.json({
          message: emailValidation.message
        }, { status: 400 });
      }

        // Check if user already exists
        const existingUser = await prisma.user.findUnique({
          where: { email: email.toLowerCase().trim() }
        });

        if (existingUser) {
          return NextResponse.json({
            message: "User already exists"
          }, { status: 409 });
        }

        // Hash the password with stronger rounds
        const hashedPassword = await bcrypt.hash(password, 12);

        // Clean email for storage (already sanitized by schema)
        const cleanEmail = email.toLowerCase().trim();

        // Create the new user (unverified)
        const user = await prisma.user.create({
          data: {
            email: cleanEmail,
            password: hashedPassword,
            emailVerified: null, // Explicitly set as unverified
          },
        });

        // Generate verification token
        const verificationToken = uuidv4();
        const tokenExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

        // Store verification token
        await prisma.verificationToken.create({
          data: {
            identifier: cleanEmail,
            token: verificationToken,
            expires: tokenExpiry,
          },
        });

        // Send verification email
        const verificationUrl = `${process.env.NEXTAUTH_URL}/auth/verify-email?token=${verificationToken}&email=${encodeURIComponent(cleanEmail)}`;

        try {
          await sendEmail({
            to: cleanEmail,
            subject: "Verify your email for FAAFO Career Platform",
            template: React.createElement(VerificationEmail, {
              username: cleanEmail,
              verificationLink: verificationUrl
            }),
          });
          console.log(`Verification email sent to ${cleanEmail}`);
        } catch (emailError) {
          console.error('Failed to send verification email:', emailError);
          // Don't fail the registration if email fails, but log it
        }

      return NextResponse.json({
        message: "User created successfully. Please check your email to verify your account.",
        requiresVerification: true
      }, { status: 201 });

    } catch (error) {
      console.error("Error during signup:", error);
      return NextResponse.json({
        message: "Registration failed. Please try again."
      }, { status: 500 });
    }
  },
  {
    requireCSRF: true,
    rateLimit: { max: 5, windowMs: 15 * 60 * 1000 } // 5 attempts per 15 minutes
  }
);