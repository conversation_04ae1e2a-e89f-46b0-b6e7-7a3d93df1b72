import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { geminiService } from '@/lib/services/geminiService';
import { cacheService, aiCacheKeys } from '@/lib/services/cacheService';
import { withErrorHandler } from '@/lib/errorHandler';
import { withRateLimit } from '@/lib/rateLimit';
import { withCSRFProtection } from '@/lib/csrf';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { skillGapPerformanceMonitor } from '@/lib/performance/skill-gap-performance';
import { edgeCaseHandlerService } from '@/lib/skills/EdgeCaseHandlerService';

// Enhanced validation schema for comprehensive analysis
const comprehensiveSkillsAnalysisSchema = z.object({
  currentSkills: z.array(z.object({
    skillId: z.string().optional(),
    skillName: z.string().min(1, 'Skill name is required'),
    selfRating: z.number().min(1).max(10),
    confidenceLevel: z.number().min(1).max(10),
    lastUsed: z.string().optional(),
    yearsOfExperience: z.number().min(0).max(50).optional(),
  })).min(0, 'Skills array cannot be negative').max(50, 'Too many skills'), // Allow empty array
  
  targetCareerPath: z.object({
    careerPathId: z.string().optional(),
    careerPathName: z.string().min(2, 'Career path name is required'),
    targetLevel: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']),
  }),
  
  preferences: z.object({
    timeframe: z.enum(['THREE_MONTHS', 'SIX_MONTHS', 'ONE_YEAR', 'TWO_YEARS', 'CUSTOM']),
    hoursPerWeek: z.number().min(1).max(80),
    learningStyle: z.array(z.string()).optional().default([]),
    budget: z.enum(['FREE', 'FREEMIUM', 'PAID', 'ANY']).default('ANY'),
    focusAreas: z.array(z.string()).optional().default([]),
  }),
  
  includeMarketData: z.boolean().default(true),
  includePersonalizedPaths: z.boolean().default(true),
});

type ComprehensiveSkillsAnalysisRequest = z.infer<typeof comprehensiveSkillsAnalysisSchema>;

interface ComprehensiveSkillsAnalysisResponse {
  success: boolean;
  data: {
    analysisId: string;
    skillGaps: Array<{
      skillId: string;
      skillName: string;
      currentLevel: number;
      targetLevel: number;
      gapSeverity: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';
      priority: number;
      estimatedLearningTime: number;
      marketDemand?: string;
      salaryImpact?: number;
    }>;
    learningPlan: {
      totalEstimatedHours: number;
      milestones: Array<{
        month: number;
        skills: string[];
        estimatedHours: number;
        learningPaths: string[];
      }>;
      recommendedResources: Array<{
        resourceId: string;
        resourceType: string;
        priority: string;
        skillsAddressed: string[];
        estimatedHours: number;
      }>;
    };
    careerReadiness: {
      currentScore: number;
      targetScore: number;
      improvementPotential: number;
      timeToTarget: number;
    };
    marketInsights?: {
      industryTrends: Array<{
        skill: string;
        trend: string;
        demandLevel: string;
      }>;
      salaryProjections: {
        currentEstimate: number;
        targetEstimate: number;
        improvementPotential: number;
      };
    };
  };
  cached: boolean;
  generatedAt: string;
}

async function getUserSkillAssessments(userId: string) {
  try {
    const assessments = await prisma.skillAssessment.findMany({
      where: {
        userId,
        isActive: true,
      },
      include: {
        skill: true,
      },
      orderBy: {
        assessmentDate: 'desc',
      },
    });

    return assessments.map(assessment => ({
      skillId: assessment.skillId,
      skillName: assessment.skill.name,
      selfRating: assessment.selfRating,
      confidenceLevel: assessment.confidenceLevel,
      lastAssessed: assessment.assessmentDate,
      assessmentType: assessment.assessmentType,
    }));
  } catch (error) {
    console.error('Error fetching user skill assessments:', error);
    return [];
  }
}

async function getSkillsFromCareerAssessment(userId: string) {
  try {
    const assessment = await prisma.assessment.findFirst({
      where: {
        userId,
        status: 'COMPLETED',
      },
      include: {
        responses: true,
      },
      orderBy: {
        completedAt: 'desc',
      },
    });

    if (!assessment) {
      return [];
    }

    const skillsFromAssessment: Array<{
      skillName: string;
      selfRating: number;
      confidenceLevel: number;
      lastUsed: string;
    }> = [];

    // Extract skills from assessment responses
    assessment.responses.forEach(response => {
      try {
        const value = typeof response.answerValue === 'string'
          ? JSON.parse(response.answerValue)
          : response.answerValue;

        // Look for skills-related questions
        if (response.questionKey.toLowerCase().includes('skill') && Array.isArray(value)) {
          value.forEach((skill: string) => {
            if (typeof skill === 'string' && skill.trim()) {
              skillsFromAssessment.push({
                skillName: skill.trim(),
                selfRating: 6, // Default moderate rating
                confidenceLevel: 6, // Default moderate confidence
                lastUsed: assessment.completedAt?.toISOString() || new Date().toISOString(),
              });
            }
          });
        }

        // Look for experience level indicators
        if (response.questionKey.toLowerCase().includes('experience') && typeof value === 'string') {
          const experienceLevel = value.toLowerCase();
          let defaultRating = 5;
          if (experienceLevel.includes('senior') || experienceLevel.includes('expert')) {
            defaultRating = 8;
          } else if (experienceLevel.includes('mid') || experienceLevel.includes('intermediate')) {
            defaultRating = 6;
          } else if (experienceLevel.includes('junior') || experienceLevel.includes('beginner')) {
            defaultRating = 4;
          }

          // Update ratings for existing skills
          skillsFromAssessment.forEach(skill => {
            skill.selfRating = defaultRating;
            skill.confidenceLevel = defaultRating;
          });
        }
      } catch (error) {
        console.error('Error parsing assessment response:', error);
      }
    });

    return skillsFromAssessment;
  } catch (error) {
    console.error('Error fetching skills from career assessment:', error);
    return [];
  }
}

async function getEnhancedCareerPathData(careerPathId?: string, careerPathName?: string) {
  try {
    const whereClause = careerPathId 
      ? { id: careerPathId }
      : {
          OR: [
            { name: { contains: careerPathName, mode: 'insensitive' as const } },
            { slug: careerPathName?.toLowerCase().replace(/\s+/g, '-') }
          ]
        };

    const careerPath = await prisma.careerPath.findFirst({
      where: whereClause,
      include: {
        relatedSkills: {
          include: {
            marketData: {
              where: { isActive: true },
              orderBy: { dataDate: 'desc' },
              take: 1,
            },
          },
        },
        learningResources: {
          where: { isActive: true },
          include: {
            skills: true,
            ratings: {
              select: {
                rating: true,
              },
            },
          },
        },
        learningPaths: {
          where: { isActive: true },
          include: {
            skills: true,
            _count: {
              select: {
                steps: true,
              },
            },
          },
        },
      },
    });

    if (!careerPath) return null;

    return {
      id: careerPath.id,
      name: careerPath.name,
      requiredSkills: careerPath.relatedSkills.map(skill => ({
        id: skill.id,
        name: skill.name,
        category: skill.category,
        marketData: skill.marketData[0] || null,
      })),
      learningResources: careerPath.learningResources.map(resource => ({
        id: resource.id,
        title: resource.title,
        type: resource.type,
        skillLevel: resource.skillLevel,
        cost: resource.cost,
        averageRating: resource.ratings.length > 0 
          ? resource.ratings.reduce((sum, r) => sum + r.rating, 0) / resource.ratings.length 
          : 0,
        skills: resource.skills?.map(skill => skill.name) || [],
      })),
      learningPaths: careerPath.learningPaths.map(path => ({
        id: path.id,
        title: path.title,
        difficulty: path.difficulty,
        estimatedHours: path.estimatedHours,
        stepCount: path._count.steps,
        skills: path.skills?.map(skill => skill.name) || [],
      })),
    };
  } catch (error) {
    console.error('Error fetching enhanced career path data:', error);
    return null;
  }
}

async function createSkillGapAnalysis(
  userId: string,
  request: ComprehensiveSkillsAnalysisRequest,
  analysisData: any,
  careerPathData: any
) {
  try {
    const expiresAt = new Date();
    expiresAt.setMonth(expiresAt.getMonth() + 3); // Analysis valid for 3 months

    const skillGapAnalysis = await prisma.skillGapAnalysis.create({
      data: {
        userId,
        targetCareerPathId: request.targetCareerPath.careerPathId || null,
        targetCareerPathName: request.targetCareerPath.careerPathName,
        experienceLevel: request.targetCareerPath.targetLevel,
        timeframe: request.preferences.timeframe,
        analysisData: analysisData,
        skillGaps: analysisData.skillGaps || [],
        learningPlan: analysisData.learningPlan || {},
        marketData: analysisData.marketInsights || null,
        progressTracking: {
          milestones: analysisData.learningPlan?.milestones || [],
          completedMilestones: [],
          currentPhase: 'planning',
        },
        status: 'ACTIVE',
        completionPercentage: 0,
        expiresAt,
      },
    });

    return skillGapAnalysis;
  } catch (error) {
    console.error('Error creating skill gap analysis:', error);
    throw error;
  }
}

async function handleComprehensiveSkillsAnalysis(request: NextRequest): Promise<NextResponse> {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    return NextResponse.json(
      { success: false, error: 'Authentication required' },
      { status: 401 }
    );
  }

  const userId = session.user.id;

  try {
    const body = await request.json();
    const validation = comprehensiveSkillsAnalysisSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid request data',
          details: validation.error.errors 
        },
        { status: 400 }
      );
    }

    const requestData = validation.data;

    // Generate cache key
    const cacheKey = aiCacheKeys.skillsAnalysis(
      userId, 
      `comprehensive_${requestData.targetCareerPath.careerPathName}_${requestData.targetCareerPath.targetLevel}_${requestData.preferences.timeframe}`
    );

    // Check cache first
    const cached = await cacheService.getJSON(cacheKey);
    if (cached) {
      return NextResponse.json({
        success: true,
        data: cached,
        cached: true,
        generatedAt: cached.generatedAt || new Date().toISOString(),
      });
    }

    // Get user's existing skill assessments
    const userAssessments = await getUserSkillAssessments(userId);

    // Get skills from career assessment if no skill assessments exist
    const careerAssessmentSkills = userAssessments.length === 0
      ? await getSkillsFromCareerAssessment(userId)
      : [];
    
    // Get enhanced career path data
    const careerPathData = await getEnhancedCareerPathData(
      requestData.targetCareerPath.careerPathId,
      requestData.targetCareerPath.careerPathName
    ) || {
      // Fallback data for TDD
      id: 'fallback-career-path-id',
      name: requestData.targetCareerPath.careerPathName,
      requiredSkills: [],
      learningResources: [],
      learningPaths: [],
    };

    // Prepare data for AI analysis
    const allCurrentSkills = [
      ...requestData.currentSkills,
      ...userAssessments.map(assessment => ({
        skillName: assessment.skillName,
        selfRating: assessment.selfRating,
        confidenceLevel: assessment.confidenceLevel,
        lastUsed: assessment.lastAssessed.toISOString(),
      })),
      ...careerAssessmentSkills
    ];

    // Remove duplicates based on skill name
    const uniqueSkills = allCurrentSkills.reduce((acc, skill) => {
      const existing = acc.find(s => s.skillName.toLowerCase() === skill.skillName.toLowerCase());
      if (!existing) {
        acc.push(skill);
      } else if (skill.selfRating && skill.selfRating > (existing.selfRating || 0)) {
        // Keep the higher rating if duplicate
        Object.assign(existing, skill);
      }
      return acc;
    }, [] as typeof allCurrentSkills);

    // Perform comprehensive AI analysis with EdgeCaseHandler and performance monitoring
    const responseData = await skillGapPerformanceMonitor.monitorSkillAnalysis(
      requestData,
      async () => {
        // Use EdgeCaseHandler for comprehensive error handling
        const edgeCaseResult = await edgeCaseHandlerService.handleLearningPathGeneration({
          userId,
          currentSkills: uniqueSkills.map(skill => ({
            skill: skill.skillName,
            level: skill.selfRating,
            confidence: skill.confidenceLevel
          })),
          targetRole: requestData.targetCareerPath.careerPathName,
          timeframe: requestData.preferences.timeframe === 'THREE_MONTHS' ? 3 :
                    requestData.preferences.timeframe === 'SIX_MONTHS' ? 6 :
                    requestData.preferences.timeframe === 'ONE_YEAR' ? 12 :
                    requestData.preferences.timeframe === 'TWO_YEARS' ? 24 : 12,
          learningStyle: 'balanced',
          availability: requestData.preferences.hoursPerWeek,
          budget: requestData.preferences.budget === 'FREE' ? 0 :
                 requestData.preferences.budget === 'FREEMIUM' ? 100 :
                 requestData.preferences.budget === 'PAID' ? 1000 : 500
        });

        // If EdgeCaseHandler fails, fall back to direct AI analysis
        if (!edgeCaseResult.success) {
          console.warn('EdgeCaseHandler failed, falling back to direct analysis:', edgeCaseResult.error);
        }

        const analysisResult = await geminiService.analyzeComprehensiveSkillGap(
          uniqueSkills,
          requestData.targetCareerPath,
          requestData.preferences,
          careerPathData,
          userId
        );

        if (!analysisResult.success) {
          // If both EdgeCaseHandler and direct analysis fail, use fallback data
          if (edgeCaseResult.fallbackData) {
            return {
              skillGaps: [],
              learningPlan: edgeCaseResult.fallbackData,
              careerReadiness: { currentScore: 0, targetScore: 100, improvementPotential: 100, timeToTarget: 12 },
              marketInsights: undefined,
              edgeCaseHandlerUsed: true,
              fallbackDataUsed: true
            };
          }
          throw new Error(analysisResult.error || 'Failed to perform comprehensive skills analysis');
        }

        // Create skill gap analysis record
        const skillGapAnalysis = await createSkillGapAnalysis(
          userId,
          requestData,
          analysisResult.data,
          careerPathData
        );

        // Prepare comprehensive response
        const responseData: ComprehensiveSkillsAnalysisResponse['data'] = {
          analysisId: skillGapAnalysis.id,
          skillGaps: analysisResult.data.skillGaps || [],
          learningPlan: analysisResult.data.learningPlan || {
            totalEstimatedHours: 0,
            milestones: [],
            recommendedResources: [],
          },
          careerReadiness: analysisResult.data.careerReadiness || {
            currentScore: 0,
            targetScore: 100,
            improvementPotential: 100,
            timeToTarget: 12,
          },
          marketInsights: requestData.includeMarketData ? analysisResult.data.marketInsights : undefined,
        };

        return responseData;
      },
      userId
    );

    // Cache the result for 6 hours
    await cacheService.setJSON(cacheKey, responseData, 6 * 60 * 60);

    // Track usage analytics
    console.log(`Comprehensive skills analysis completed for user ${userId}, career: ${requestData.targetCareerPath.careerPathName}`);

    return NextResponse.json({
      success: true,
      data: responseData,
      cached: false,
      generatedAt: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Comprehensive skills analysis error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'An unexpected error occurred during comprehensive skills analysis' 
      },
      { status: 500 }
    );
  }
}

// POST endpoint for comprehensive analysis
export const POST = withErrorHandler(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 5 }, // 5 comprehensive analyses per 15 minutes
      () => handleComprehensiveSkillsAnalysis(request)
    );
  });
});
