/**
 * Monitoring Dashboard API
 * Provides real-time monitoring data for the skill gap analyzer
 */

import { NextRequest, NextResponse } from 'next/server';
import { skillGapMetrics } from '@/lib/monitoring/metrics';
import { skillGapAlerting } from '@/lib/monitoring/alerting';
import { skillGapLogger } from '@/lib/monitoring/logger';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const timeWindow = parseInt(searchParams.get('timeWindow') || '3600000'); // 1 hour default
    const includeAlerts = searchParams.get('includeAlerts') === 'true';
    const includeMetrics = searchParams.get('includeMetrics') !== 'false';

    const dashboardData: any = {
      timestamp: Date.now(),
      timeWindow,
    };

    // Get system health
    const systemHealth = skillGapMetrics.getSystemHealth();
    dashboardData.systemHealth = systemHealth;

    // Get metrics if requested
    if (includeMetrics) {
      const metrics = skillGapMetrics.exportMetrics(timeWindow);
      dashboardData.metrics = metrics;

      // Get metric statistics
      const metricStats: Record<string, any> = {};
      Object.keys(metrics).forEach(metricName => {
        const stats = skillGapMetrics.getMetricStats(metricName, timeWindow);
        if (stats) {
          metricStats[metricName] = stats;
        }
      });
      dashboardData.metricStats = metricStats;
    }

    // Get alerts if requested
    if (includeAlerts) {
      const activeAlerts = skillGapAlerting.getActiveAlerts();
      const alertStats = skillGapAlerting.getAlertingStats();
      
      dashboardData.alerts = {
        active: activeAlerts,
        stats: alertStats,
      };
    }

    // Get performance summary
    dashboardData.performance = {
      averageResponseTime: skillGapMetrics.getMetricStats('skill_search_duration', timeWindow)?.avg || 0,
      throughput: skillGapMetrics.getMetricStats('skill_assessments_completed', timeWindow)?.count || 0,
      errorRate: skillGapMetrics.getMetricStats('error_rate', timeWindow)?.avg || 0,
      cacheHitRate: skillGapMetrics.getMetricStats('skill_search_cache_hit_rate', timeWindow)?.avg || 0,
    };

    // Log dashboard access
    skillGapLogger.logUserBehavior({
      event: 'monitoring_dashboard_accessed',
      properties: {
        timeWindow,
        includeAlerts,
        includeMetrics,
        systemStatus: systemHealth.status,
      },
    });

    return NextResponse.json(dashboardData);
  } catch (error) {
    skillGapLogger.logError(error as Error, {
      feature: 'monitoring_dashboard',
      action: 'get_dashboard_data',
    });

    return NextResponse.json(
      { error: 'Failed to fetch dashboard data' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, data } = body;

    switch (action) {
      case 'acknowledge_alert':
        const { alertId, acknowledgedBy } = data;
        const acknowledged = skillGapAlerting.acknowledgeAlert(alertId, acknowledgedBy);
        
        return NextResponse.json({ 
          success: acknowledged,
          message: acknowledged ? 'Alert acknowledged' : 'Failed to acknowledge alert'
        });

      case 'resolve_alert':
        const { alertId: resolveAlertId, resolvedBy } = data;
        const resolved = skillGapAlerting.resolveAlert(resolveAlertId, resolvedBy);
        
        return NextResponse.json({ 
          success: resolved,
          message: resolved ? 'Alert resolved' : 'Failed to resolve alert'
        });

      case 'test_notification':
        const { channelId } = data;
        const testResult = await skillGapAlerting.testNotificationChannel(channelId);
        
        return NextResponse.json({ 
          success: testResult,
          message: testResult ? 'Notification test successful' : 'Notification test failed'
        });

      case 'create_test_alert':
        const testAlertId = await skillGapAlerting.createAlert({
          title: 'Test Alert',
          message: 'This is a test alert created from the monitoring dashboard',
          severity: data.severity || 'low',
          source: 'monitoring_dashboard',
          metadata: { createdBy: data.createdBy },
        });
        
        return NextResponse.json({
          success: true,
          alertId: testAlertId,
          message: 'Test alert created'
        });

      default:
        return NextResponse.json(
          { error: 'Unknown action' },
          { status: 400 }
        );
    }
  } catch (error) {
    skillGapLogger.logError(error as Error, {
      feature: 'monitoring_dashboard',
      action: 'post_dashboard_action',
    });

    return NextResponse.json(
      { error: 'Failed to process dashboard action' },
      { status: 500 }
    );
  }
}
