/**
 * SEO Optimization for Skill Gap Analyzer
 * Provides comprehensive SEO metadata, structured data, and optimization utilities
 */

import { Metadata } from 'next';

export interface SkillGapSEOData {
  careerPath?: string;
  skillCount?: number;
  analysisType?: string;
  userLevel?: string;
  completionRate?: number;
}

export interface StructuredDataConfig {
  type: 'assessment' | 'analysis' | 'results' | 'learning-path';
  data: any;
}

/**
 * Generate SEO metadata for skill gap analyzer pages
 */
export function generateSkillGapSEO(
  page: 'main' | 'assessment' | 'analysis' | 'results',
  data?: SkillGapSEOData
): Metadata {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://faafo.com';
  const baseTitle = 'FAAFO Career Platform';
  
  switch (page) {
    case 'main':
      return {
        title: `Skill Gap Analyzer - AI Assessment | FAAFO`,
        description: 'Discover skill gaps with AI analysis. Get personalized learning paths and career readiness scores to advance your career.',
        keywords: [
          'skill gap analysis',
          'career assessment',
          'professional development',
          'skill evaluation',
          'career planning',
          'learning path',
          'AI career analysis',
          'skill assessment tool',
          'career readiness',
          'professional skills'
        ].join(', '),
        openGraph: {
          title: 'AI-Powered Skill Gap Analysis for Career Growth',
          description: 'Identify skill gaps, get personalized learning recommendations, and track your career readiness with our comprehensive skill analyzer.',
          type: 'website',
          url: `${baseUrl}/skills/gap-analyzer`,
          images: [
            {
              url: `${baseUrl}/images/skill-gap-analyzer-og.jpg`,
              width: 1200,
              height: 630,
              alt: 'Skill Gap Analyzer - Career Development Tool',
            },
          ],
          siteName: baseTitle,
        },
        twitter: {
          card: 'summary_large_image',
          title: 'Skill Gap Analyzer - AI-Powered Career Assessment',
          description: 'Discover your skill gaps and get personalized learning paths to advance your career.',
          images: [`${baseUrl}/images/skill-gap-analyzer-twitter.jpg`],
          creator: '@faafo_platform',
        },
        robots: {
          index: true,
          follow: true,
          googleBot: {
            index: true,
            follow: true,
            'max-video-preview': -1,
            'max-image-preview': 'large',
            'max-snippet': -1,
          },
        },
        alternates: {
          canonical: `${baseUrl}/skills/gap-analyzer`,
        },
      };

    case 'assessment':
      return {
        title: `Skill Assessment - Rate Skills | FAAFO`,
        description: 'Complete a skill assessment to establish your baseline. Rate proficiency and confidence across technical and soft skills.',
        keywords: [
          'skill assessment',
          'self evaluation',
          'professional skills rating',
          'competency assessment',
          'skill proficiency',
          'career evaluation'
        ].join(', '),
        openGraph: {
          title: 'Professional Skill Assessment Tool',
          description: 'Rate your skills and build a comprehensive profile for personalized career guidance.',
          type: 'website',
          url: `${baseUrl}/skills/gap-analyzer?tab=assess`,
        },
        robots: {
          index: true,
          follow: true,
        },
      };

    case 'analysis':
      const careerPathTitle = data?.careerPath ? ` for ${data.careerPath}` : '';
      return {
        title: `Gap Analysis${careerPathTitle} - AI | FAAFO`,
        description: `Get AI skill gap analysis${careerPathTitle}. Discover missing skills and personalized development recommendations.`,
        keywords: [
          'skill gap analysis',
          'AI career analysis',
          'learning recommendations',
          'career development plan',
          'skill priorities',
          data?.careerPath?.toLowerCase().replace(/\s+/g, ' ')
        ].filter(Boolean).join(', '),
        openGraph: {
          title: `AI Skill Gap Analysis${careerPathTitle}`,
          description: 'Get personalized insights into your skill gaps and receive AI-powered learning recommendations.',
          type: 'website',
          url: `${baseUrl}/skills/gap-analyzer?tab=analyze`,
        },
      };

    case 'results':
      const skillCountText = data?.skillCount ? ` (${data.skillCount} skills)` : '';
      const completionText = data?.completionRate ? ` - ${data.completionRate}% Ready` : '';

      return {
        title: `Analysis Results${completionText} | FAAFO`,
        description: `View skill gap analysis results${skillCountText}. Get learning plans, market insights, and career readiness scores.`,
        keywords: [
          'skill analysis results',
          'learning plan',
          'career readiness score',
          'skill development roadmap',
          'professional growth plan',
          'market insights'
        ].join(', '),
        openGraph: {
          title: `Your Skill Gap Analysis Results${completionText}`,
          description: 'Comprehensive analysis results with personalized learning recommendations and career insights.',
          type: 'website',
          url: `${baseUrl}/skills/gap-analyzer?tab=results`,
        },
        robots: {
          index: false, // Results are user-specific
          follow: true,
        },
      };

    default:
      return {};
  }
}

/**
 * Generate structured data (JSON-LD) for skill gap analyzer
 */
export function generateStructuredData(config: StructuredDataConfig): object {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://faafo.com';
  
  const baseStructure = {
    '@context': 'https://schema.org',
    '@type': 'WebApplication',
    name: 'FAAFO Skill Gap Analyzer',
    url: `${baseUrl}/skills/gap-analyzer`,
    description: 'AI-powered skill gap analysis tool for career development',
    applicationCategory: 'EducationalApplication',
    operatingSystem: 'Web Browser',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
    },
    provider: {
      '@type': 'Organization',
      name: 'FAAFO Career Platform',
      url: baseUrl,
    },
  };

  switch (config.type) {
    case 'assessment':
      return {
        ...baseStructure,
        '@type': 'Assessment',
        name: 'Professional Skill Assessment',
        description: 'Comprehensive skill evaluation tool for career development',
        educationalLevel: 'Professional',
        assesses: 'Professional Skills and Competencies',
        educationalUse: 'Career Development',
      };

    case 'analysis':
      return {
        ...baseStructure,
        '@type': 'AnalysisNewsArticle',
        headline: 'Skill Gap Analysis Results',
        description: 'AI-powered analysis of professional skill gaps and learning recommendations',
        author: {
          '@type': 'Organization',
          name: 'FAAFO AI Analysis Engine',
        },
        publisher: {
          '@type': 'Organization',
          name: 'FAAFO Career Platform',
          logo: {
            '@type': 'ImageObject',
            url: `${baseUrl}/images/logo.png`,
          },
        },
      };

    case 'results':
      return {
        ...baseStructure,
        '@type': 'Report',
        name: 'Skill Gap Analysis Report',
        description: 'Comprehensive skill gap analysis with learning recommendations',
        about: config.data?.careerPath || 'Professional Development',
        dateCreated: new Date().toISOString(),
        author: {
          '@type': 'SoftwareApplication',
          name: 'FAAFO AI Analysis Engine',
        },
      };

    case 'learning-path':
      return {
        '@context': 'https://schema.org',
        '@type': 'LearningResource',
        name: 'Personalized Learning Path',
        description: 'AI-generated learning path based on skill gap analysis',
        educationalLevel: config.data?.level || 'Professional',
        learningResourceType: 'Learning Path',
        teaches: config.data?.skills || [],
        timeRequired: config.data?.duration || 'Variable',
        provider: {
          '@type': 'Organization',
          name: 'FAAFO Career Platform',
        },
      };

    default:
      return baseStructure;
  }
}

/**
 * Generate breadcrumb structured data
 */
export function generateBreadcrumbData(currentPage: string): object {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://faafo.com';
  
  const breadcrumbs = [
    { name: 'Home', url: baseUrl },
    { name: 'Skills', url: `${baseUrl}/skills` },
    { name: 'Gap Analyzer', url: `${baseUrl}/skills/gap-analyzer` },
  ];

  if (currentPage !== 'main') {
    const pageNames = {
      assessment: 'Skill Assessment',
      analysis: 'Gap Analysis',
      results: 'Results',
    };
    breadcrumbs.push({
      name: pageNames[currentPage as keyof typeof pageNames] || currentPage,
      url: `${baseUrl}/skills/gap-analyzer?tab=${currentPage}`,
    });
  }

  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url,
    })),
  };
}

/**
 * Generate FAQ structured data for skill gap analyzer
 */
export function generateFAQData(): object {
  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: [
      {
        '@type': 'Question',
        name: 'What is a skill gap analysis?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: 'A skill gap analysis identifies the difference between your current skills and the skills required for your target career path. It helps you understand what skills to develop for career advancement.',
        },
      },
      {
        '@type': 'Question',
        name: 'How accurate is the AI-powered analysis?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: 'Our AI analysis uses advanced algorithms and real-time market data to provide highly accurate skill gap assessments. The analysis is continuously improved based on industry trends and user feedback.',
        },
      },
      {
        '@type': 'Question',
        name: 'How long does the skill assessment take?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: 'The skill assessment typically takes 10-15 minutes to complete, depending on the number of skills you evaluate. You can save your progress and return later if needed.',
        },
      },
      {
        '@type': 'Question',
        name: 'Can I update my skill assessments over time?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: 'Yes, you can update your skill assessments at any time as you develop new skills or gain more experience. We recommend updating your assessment every 3-6 months.',
        },
      },
    ],
  };
}

/**
 * SEO-friendly URL generator for skill gap analyzer
 */
export function generateSEOUrl(
  page: string,
  params?: { careerPath?: string; skillId?: string; analysisId?: string }
): string {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://faafo.com';
  let url = `${baseUrl}/skills/gap-analyzer`;

  if (page !== 'main') {
    url += `?tab=${page}`;
  }

  if (params?.careerPath) {
    const slug = params.careerPath.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
    url += `${page === 'main' ? '?' : '&'}career=${slug}`;
  }

  if (params?.skillId) {
    url += `${url.includes('?') ? '&' : '?'}skill=${params.skillId}`;
  }

  if (params?.analysisId) {
    url += `${url.includes('?') ? '&' : '?'}analysis=${params.analysisId}`;
  }

  return url;
}
