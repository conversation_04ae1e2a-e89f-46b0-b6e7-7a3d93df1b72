/**
 * Unified Authentication State Manager
 * Ensures consistent authentication state across the application
 */

import { getSession, signOut } from 'next-auth/react';
import { User } from '@prisma/client';

export interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;
  sessionId: string | null;
  lastActivity: number | null;
  isAdmin: boolean;
  error: string | null;
}

export interface AuthStateManager {
  getState(): AuthState;
  refreshState(): Promise<void>;
  validateSession(): Promise<boolean>;
  clearState(): void;
  updateLastActivity(): void;
  checkSessionExpiry(): boolean;
  forceLogout(reason?: string): Promise<void>;
  addStateChangeListener(listener: (state: AuthState) => void): () => void;
  removeStateChangeListener(listener: (state: AuthState) => void): void;
  updateAdminStatus(): Promise<void>;
}

class AuthenticationStateManager implements AuthStateManager {
  private state: AuthState = {
    isAuthenticated: false,
    isLoading: true,
    user: null,
    sessionId: null,
    lastActivity: null,
    isAdmin: false,
    error: null
  };

  private stateChangeListeners: Set<(state: AuthState) => void> = new Set();

  private listeners: Set<(state: AuthState) => void> = new Set();
  private sessionCheckInterval: NodeJS.Timeout | null = null;
  private readonly SESSION_CHECK_INTERVAL = 10 * 60 * 1000; // 10 minutes (reduced from 1 minute)
  private readonly SESSION_TIMEOUT = 30 * 24 * 60 * 60 * 1000; // 30 days in ms
  private readonly ACTIVITY_TIMEOUT = 24 * 60 * 60 * 1000; // 24 hours in ms

  constructor() {
    this.initializeSessionMonitoring();
  }

  /**
   * Get current authentication state
   */
  getState(): AuthState {
    return { ...this.state };
  }

  /**
   * Subscribe to state changes
   */
  subscribe(listener: (state: AuthState) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  /**
   * Notify all listeners of state changes
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.getState()));
  }

  /**
   * Update state and notify listeners
   */
  private setState(updates: Partial<AuthState>): void {
    const oldState = { ...this.state };
    this.state = { ...this.state, ...updates };

    // Notify listeners
    this.notifyListeners();

    // Broadcast state change to other tabs
    this.broadcastStateChange(oldState, this.state);
  }

  /**
   * Broadcast state changes to other tabs via localStorage
   */
  private broadcastStateChange(oldState: AuthState, newState: AuthState): void {
    if (typeof window === 'undefined') return;

    // Only broadcast significant changes
    const significantChange =
      oldState.isAuthenticated !== newState.isAuthenticated ||
      oldState.user?.id !== newState.user?.id ||
      oldState.isAdmin !== newState.isAdmin;

    if (significantChange) {
      try {
        localStorage.setItem('auth_state_change', JSON.stringify({
          timestamp: Date.now(),
          isAuthenticated: newState.isAuthenticated,
          userId: newState.user?.id,
          isAdmin: newState.isAdmin
        }));

        // Remove the item immediately to trigger storage event
        localStorage.removeItem('auth_state_change');
      } catch (error) {
        console.error('Failed to broadcast auth state change:', error);
      }
    }
  }

  /**
   * Refresh authentication state from session
   */
  async refreshState(): Promise<void> {
    try {
      this.setState({ isLoading: true, error: null });

      const session = await getSession();
      
      if (session?.user) {
        // Get admin status from session if available, otherwise default to false
        // Admin status will be updated separately via API call if needed
        const isAdmin = (session as any).user?.isAdmin || false;

        this.setState({
          isAuthenticated: true,
          isLoading: false,
          user: session.user as User,
          sessionId: (session as any).sessionId || null,
          lastActivity: Date.now(),
          isAdmin,
          error: null
        });

        this.updateLastActivity();
      } else {
        this.setState({
          isAuthenticated: false,
          isLoading: false,
          user: null,
          sessionId: null,
          lastActivity: null,
          isAdmin: false,
          error: null
        });
      }
    } catch (error) {
      console.error('Error refreshing auth state:', error);
      this.setState({
        isAuthenticated: false,
        isLoading: false,
        user: null,
        sessionId: null,
        lastActivity: null,
        isAdmin: false,
        error: error instanceof Error ? error.message : 'Authentication error'
      });
    }
  }

  /**
   * Validate current session
   */
  async validateSession(): Promise<boolean> {
    try {
      // Check session expiry
      if (this.checkSessionExpiry()) {
        await this.forceLogout('Session expired');
        return false;
      }

      // Validate with server
      const response = await fetch('/api/auth/validate-session', {
        method: 'GET',
        credentials: 'same-origin',
        headers: {
          'Cache-Control': 'no-cache'
        }
      });

      if (!response.ok) {
        if (response.status === 401) {
          await this.forceLogout('Session invalid');
          return false;
        }
        throw new Error(`Session validation failed: ${response.status}`);
      }

      const { valid, user, isAdmin } = await response.json();

      if (valid && user) {
        this.setState({
          isAuthenticated: true,
          user,
          isAdmin: isAdmin || false,
          lastActivity: Date.now(),
          error: null
        });
        return true;
      } else {
        await this.forceLogout('Session validation failed');
        return false;
      }
    } catch (error) {
      console.error('Session validation error:', error);
      this.setState({
        error: error instanceof Error ? error.message : 'Session validation error'
      });
      return false;
    }
  }

  /**
   * Clear authentication state
   */
  clearState(): void {
    this.setState({
      isAuthenticated: false,
      isLoading: false,
      user: null,
      sessionId: null,
      lastActivity: null,
      isAdmin: false,
      error: null
    });
  }

  /**
   * Update last activity timestamp
   */
  updateLastActivity(): void {
    const now = Date.now();
    this.setState({ lastActivity: now });
    
    // Store in localStorage for persistence across tabs
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_last_activity', now.toString());
    }
  }

  /**
   * Check if session has expired
   */
  checkSessionExpiry(): boolean {
    const { lastActivity } = this.state;
    
    if (!lastActivity) {
      return true;
    }

    const now = Date.now();
    const timeSinceActivity = now - lastActivity;

    // Check activity timeout
    if (timeSinceActivity > this.ACTIVITY_TIMEOUT) {
      return true;
    }

    // Check absolute session timeout
    if (timeSinceActivity > this.SESSION_TIMEOUT) {
      return true;
    }

    return false;
  }

  /**
   * Force logout with reason
   */
  async forceLogout(reason?: string): Promise<void> {
    try {
      console.log('Force logout:', reason);
      
      // Clear local state first
      this.clearState();
      
      // Clear localStorage
      if (typeof window !== 'undefined') {
        localStorage.removeItem('auth_last_activity');
        localStorage.removeItem('auth_session_id');
      }

      // Sign out from NextAuth
      await signOut({ 
        redirect: false,
        callbackUrl: '/login'
      });

      // Set error state if reason provided
      if (reason) {
        this.setState({ error: reason });
      }
    } catch (error) {
      console.error('Error during force logout:', error);
      this.setState({
        error: error instanceof Error ? error.message : 'Logout error'
      });
    }
  }

  /**
   * Update admin status via API call (optional, called separately)
   */
  async updateAdminStatus(): Promise<void> {
    const currentState = this.getState();
    if (!currentState.isAuthenticated || !currentState.user?.id) {
      return;
    }

    try {
      const response = await fetch(`/api/auth/check-admin?userId=${currentState.user.id}`, {
        method: 'GET',
        credentials: 'same-origin',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const { isAdmin } = await response.json();
        this.setState({ isAdmin: isAdmin || false });
      }
    } catch (error) {
      console.error('Error updating admin status:', error);
    }
  }

  /**
   * Initialize session monitoring
   */
  private initializeSessionMonitoring(): void {
    // Initial state refresh
    this.refreshState();

    // Set up periodic session validation
    this.sessionCheckInterval = setInterval(() => {
      if (this.state.isAuthenticated) {
        this.validateSession();
      }
    }, this.SESSION_CHECK_INTERVAL);

    // Listen for storage events (cross-tab synchronization)
    if (typeof window !== 'undefined') {
      window.addEventListener('storage', (event) => {
        if (event.key === 'auth_last_activity') {
          const lastActivity = event.newValue ? parseInt(event.newValue) : null;
          if (lastActivity) {
            this.setState({ lastActivity });
          }
        }
      });

      // Listen for focus events to refresh state
      window.addEventListener('focus', () => {
        if (this.state.isAuthenticated) {
          this.refreshState();
        }
      });

      // Listen for beforeunload to update activity
      window.addEventListener('beforeunload', () => {
        this.updateLastActivity();
      });
    }
  }

  /**
   * Add state change listener (alias for subscribe)
   */
  addStateChangeListener(listener: (state: AuthState) => void): () => void {
    return this.subscribe(listener);
  }

  /**
   * Remove state change listener
   */
  removeStateChangeListener(listener: (state: AuthState) => void): void {
    this.listeners.delete(listener);
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.sessionCheckInterval) {
      clearInterval(this.sessionCheckInterval);
      this.sessionCheckInterval = null;
    }
    this.listeners.clear();
  }
}

// Singleton instance
let authStateManager: AuthenticationStateManager | null = null;

/**
 * Get the global authentication state manager instance
 */
export function getAuthStateManager(): AuthenticationStateManager {
  if (!authStateManager) {
    authStateManager = new AuthenticationStateManager();
  }
  return authStateManager;
}


