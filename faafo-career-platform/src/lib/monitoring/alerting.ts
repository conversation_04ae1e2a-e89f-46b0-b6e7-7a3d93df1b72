/**
 * Comprehensive Alerting System for Skill Gap Analyzer
 * Multi-channel alerting with escalation and notification management
 */

import { EventEmitter } from 'events';
import { skillGapLogger } from './logger';
import { skillGapMetrics } from './metrics';

export interface Alert {
  id: string;
  title: string;
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  source: string;
  timestamp: number;
  metadata?: Record<string, any>;
  acknowledged?: boolean;
  acknowledgedBy?: string;
  acknowledgedAt?: number;
  resolved?: boolean;
  resolvedBy?: string;
  resolvedAt?: number;
}

export interface NotificationChannel {
  id: string;
  type: 'email' | 'slack' | 'webhook' | 'sms' | 'console';
  config: Record<string, any>;
  enabled: boolean;
  severityFilter: ('low' | 'medium' | 'high' | 'critical')[];
}

export interface EscalationRule {
  id: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timeToEscalate: number; // milliseconds
  escalationChannels: string[];
  enabled: boolean;
}

class SkillGapAlerting extends EventEmitter {
  private alerts: Map<string, Alert> = new Map();
  private channels: Map<string, NotificationChannel> = new Map();
  private escalationRules: Map<string, EscalationRule> = new Map();
  private escalationTimers: Map<string, NodeJS.Timeout> = new Map();

  constructor() {
    super();
    this.setupDefaultChannels();
    this.setupDefaultEscalationRules();
    this.setupMetricsListener();
  }

  /**
   * Create and send an alert
   */
  async createAlert(alertData: Omit<Alert, 'id' | 'timestamp'>): Promise<string> {
    const alert: Alert = {
      ...alertData,
      id: this.generateAlertId(),
      timestamp: Date.now(),
    };

    this.alerts.set(alert.id, alert);

    // Log the alert
    skillGapLogger.logSecurityEvent({
      eventType: 'alert_created',
      severity: alert.severity,
      details: `${alert.title}: ${alert.message}`,
      metadata: alert.metadata,
    });

    // Send notifications
    await this.sendNotifications(alert);

    // Setup escalation if needed
    this.setupEscalation(alert);

    // Emit alert event
    this.emit('alert_created', alert);

    return alert.id;
  }

  /**
   * Acknowledge an alert
   */
  acknowledgeAlert(alertId: string, acknowledgedBy: string): boolean {
    const alert = this.alerts.get(alertId);
    if (!alert || alert.acknowledged) return false;

    alert.acknowledged = true;
    alert.acknowledgedBy = acknowledgedBy;
    alert.acknowledgedAt = Date.now();

    // Cancel escalation
    this.cancelEscalation(alertId);

    // Log acknowledgment
    skillGapLogger.logUserBehavior({
      event: 'alert_acknowledged',
      userId: acknowledgedBy,
      properties: { alertId, severity: alert.severity },
    });

    this.emit('alert_acknowledged', alert);
    return true;
  }

  /**
   * Resolve an alert
   */
  resolveAlert(alertId: string, resolvedBy: string): boolean {
    const alert = this.alerts.get(alertId);
    if (!alert || alert.resolved) return false;

    alert.resolved = true;
    alert.resolvedBy = resolvedBy;
    alert.resolvedAt = Date.now();

    // Cancel escalation
    this.cancelEscalation(alertId);

    // Log resolution
    skillGapLogger.logUserBehavior({
      event: 'alert_resolved',
      userId: resolvedBy,
      properties: { 
        alertId, 
        severity: alert.severity,
        resolutionTime: alert.resolvedAt - alert.timestamp,
      },
    });

    this.emit('alert_resolved', alert);
    return true;
  }

  /**
   * Get active alerts
   */
  getActiveAlerts(): Alert[] {
    return Array.from(this.alerts.values()).filter(
      alert => !alert.resolved
    );
  }

  /**
   * Get alerts by severity
   */
  getAlertsBySeverity(severity: Alert['severity']): Alert[] {
    return Array.from(this.alerts.values()).filter(
      alert => alert.severity === severity && !alert.resolved
    );
  }

  /**
   * Add notification channel
   */
  addNotificationChannel(channel: NotificationChannel): void {
    this.channels.set(channel.id, channel);
  }

  /**
   * Remove notification channel
   */
  removeNotificationChannel(channelId: string): void {
    this.channels.delete(channelId);
  }

  /**
   * Add escalation rule
   */
  addEscalationRule(rule: EscalationRule): void {
    this.escalationRules.set(rule.id, rule);
  }

  /**
   * Test notification channel
   */
  async testNotificationChannel(channelId: string): Promise<boolean> {
    const channel = this.channels.get(channelId);
    if (!channel || !channel.enabled) return false;

    const testAlert: Alert = {
      id: 'test-alert',
      title: 'Test Alert',
      message: 'This is a test alert to verify notification channel configuration.',
      severity: 'low',
      source: 'alerting_system',
      timestamp: Date.now(),
    };

    try {
      await this.sendToChannel(channel, testAlert);
      return true;
    } catch (error) {
      skillGapLogger.logError(error as Error, {
        feature: 'alerting',
        action: 'test_notification_channel',
        metadata: { channelId },
      });
      return false;
    }
  }

  /**
   * Get alerting statistics
   */
  getAlertingStats(): {
    totalAlerts: number;
    activeAlerts: number;
    alertsBySeverity: Record<string, number>;
    averageResolutionTime: number;
    acknowledgmentRate: number;
  } {
    const allAlerts = Array.from(this.alerts.values());
    const activeAlerts = allAlerts.filter(alert => !alert.resolved);
    
    const alertsBySeverity = {
      low: 0,
      medium: 0,
      high: 0,
      critical: 0,
    };

    allAlerts.forEach(alert => {
      alertsBySeverity[alert.severity]++;
    });

    const resolvedAlerts = allAlerts.filter(alert => alert.resolved);
    const totalResolutionTime = resolvedAlerts.reduce((sum, alert) => {
      return sum + ((alert.resolvedAt || 0) - alert.timestamp);
    }, 0);

    const acknowledgedAlerts = allAlerts.filter(alert => alert.acknowledged);

    return {
      totalAlerts: allAlerts.length,
      activeAlerts: activeAlerts.length,
      alertsBySeverity,
      averageResolutionTime: resolvedAlerts.length > 0 
        ? totalResolutionTime / resolvedAlerts.length 
        : 0,
      acknowledgmentRate: allAlerts.length > 0 
        ? (acknowledgedAlerts.length / allAlerts.length) * 100 
        : 0,
    };
  }

  /**
   * Private methods
   */
  private setupDefaultChannels(): void {
    // Console channel for development
    this.addNotificationChannel({
      id: 'console',
      type: 'console',
      config: {},
      enabled: process.env.NODE_ENV !== 'production',
      severityFilter: ['low', 'medium', 'high', 'critical'],
    });

    // Email channel for production
    if (process.env.ALERT_EMAIL_ENABLED === 'true') {
      this.addNotificationChannel({
        id: 'email',
        type: 'email',
        config: {
          smtpHost: process.env.SMTP_HOST,
          smtpPort: process.env.SMTP_PORT,
          smtpUser: process.env.SMTP_USER,
          smtpPass: process.env.SMTP_PASS,
          recipients: process.env.ALERT_EMAIL_RECIPIENTS?.split(',') || [],
        },
        enabled: true,
        severityFilter: ['medium', 'high', 'critical'],
      });
    }

    // Slack channel
    if (process.env.SLACK_WEBHOOK_URL) {
      this.addNotificationChannel({
        id: 'slack',
        type: 'slack',
        config: {
          webhookUrl: process.env.SLACK_WEBHOOK_URL,
          channel: process.env.SLACK_CHANNEL || '#alerts',
        },
        enabled: true,
        severityFilter: ['high', 'critical'],
      });
    }

    // Webhook channel
    if (process.env.ALERT_WEBHOOK_URL) {
      this.addNotificationChannel({
        id: 'webhook',
        type: 'webhook',
        config: {
          url: process.env.ALERT_WEBHOOK_URL,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': process.env.ALERT_WEBHOOK_TOKEN || '',
          },
        },
        enabled: true,
        severityFilter: ['critical'],
      });
    }
  }

  private setupDefaultEscalationRules(): void {
    this.addEscalationRule({
      id: 'critical_escalation',
      severity: 'critical',
      timeToEscalate: 300000, // 5 minutes
      escalationChannels: ['email', 'slack', 'webhook'],
      enabled: true,
    });

    this.addEscalationRule({
      id: 'high_escalation',
      severity: 'high',
      timeToEscalate: 900000, // 15 minutes
      escalationChannels: ['email', 'slack'],
      enabled: true,
    });
  }

  private setupMetricsListener(): void {
    skillGapMetrics.on('alert', async (metricAlert) => {
      await this.createAlert({
        title: `Metric Alert: ${metricAlert.metricName}`,
        message: `${metricAlert.metricName} ${metricAlert.value} exceeds threshold ${metricAlert.threshold}`,
        severity: metricAlert.severity,
        source: 'metrics_system',
        metadata: metricAlert,
      });
    });
  }

  private async sendNotifications(alert: Alert): Promise<void> {
    const applicableChannels = Array.from(this.channels.values()).filter(
      channel => 
        channel.enabled && 
        channel.severityFilter.includes(alert.severity)
    );

    const notifications = applicableChannels.map(channel => 
      this.sendToChannel(channel, alert)
    );

    await Promise.allSettled(notifications);
  }

  private async sendToChannel(channel: NotificationChannel, alert: Alert): Promise<void> {
    try {
      switch (channel.type) {
        case 'console':
          this.sendConsoleNotification(alert);
          break;
        case 'email':
          await this.sendEmailNotification(channel, alert);
          break;
        case 'slack':
          await this.sendSlackNotification(channel, alert);
          break;
        case 'webhook':
          await this.sendWebhookNotification(channel, alert);
          break;
        case 'sms':
          await this.sendSMSNotification(channel, alert);
          break;
      }
    } catch (error) {
      skillGapLogger.logError(error as Error, {
        feature: 'alerting',
        action: 'send_notification',
        metadata: { channelId: channel.id, alertId: alert.id },
      });
    }
  }

  private sendConsoleNotification(alert: Alert): void {
    const emoji = this.getSeverityEmoji(alert.severity);
    console.log(`${emoji} ALERT [${alert.severity.toUpperCase()}]: ${alert.title}`);
    console.log(`   Message: ${alert.message}`);
    console.log(`   Source: ${alert.source}`);
    console.log(`   Time: ${new Date(alert.timestamp).toISOString()}`);
    if (alert.metadata) {
      console.log(`   Metadata:`, alert.metadata);
    }
    console.log('');
  }

  private async sendEmailNotification(channel: NotificationChannel, alert: Alert): Promise<void> {
    // Email implementation would go here
    // For now, just log that we would send an email
    skillGapLogger.logUserBehavior({
      event: 'email_notification_sent',
      properties: {
        alertId: alert.id,
        severity: alert.severity,
        recipients: channel.config.recipients,
      },
    });
  }

  private async sendSlackNotification(channel: NotificationChannel, alert: Alert): Promise<void> {
    // Slack implementation would go here
    // For now, just log that we would send to Slack
    skillGapLogger.logUserBehavior({
      event: 'slack_notification_sent',
      properties: {
        alertId: alert.id,
        severity: alert.severity,
        channel: channel.config.channel,
      },
    });
  }

  private async sendWebhookNotification(channel: NotificationChannel, alert: Alert): Promise<void> {
    // Webhook implementation would go here
    // For now, just log that we would send a webhook
    skillGapLogger.logUserBehavior({
      event: 'webhook_notification_sent',
      properties: {
        alertId: alert.id,
        severity: alert.severity,
        url: channel.config.url,
      },
    });
  }

  private async sendSMSNotification(channel: NotificationChannel, alert: Alert): Promise<void> {
    // SMS implementation would go here
    // For now, just log that we would send an SMS
    skillGapLogger.logUserBehavior({
      event: 'sms_notification_sent',
      properties: {
        alertId: alert.id,
        severity: alert.severity,
      },
    });
  }

  private setupEscalation(alert: Alert): void {
    const rule = Array.from(this.escalationRules.values()).find(
      rule => rule.severity === alert.severity && rule.enabled
    );

    if (!rule) return;

    const timer = setTimeout(async () => {
      if (!alert.acknowledged && !alert.resolved) {
        await this.escalateAlert(alert, rule);
      }
    }, rule.timeToEscalate);

    this.escalationTimers.set(alert.id, timer);
  }

  private async escalateAlert(alert: Alert, rule: EscalationRule): Promise<void> {
    const escalationChannels = rule.escalationChannels
      .map(id => this.channels.get(id))
      .filter(channel => channel && channel.enabled) as NotificationChannel[];

    const escalatedAlert: Alert = {
      ...alert,
      id: this.generateAlertId(),
      title: `ESCALATED: ${alert.title}`,
      message: `This alert has been escalated due to lack of acknowledgment. Original alert: ${alert.message}`,
      timestamp: Date.now(),
    };

    for (const channel of escalationChannels) {
      await this.sendToChannel(channel, escalatedAlert);
    }

    skillGapLogger.logSecurityEvent({
      eventType: 'alert_escalated',
      severity: alert.severity,
      details: `Alert ${alert.id} escalated after ${rule.timeToEscalate}ms`,
    });

    this.emit('alert_escalated', { original: alert, escalated: escalatedAlert });
  }

  private cancelEscalation(alertId: string): void {
    const timer = this.escalationTimers.get(alertId);
    if (timer) {
      clearTimeout(timer);
      this.escalationTimers.delete(alertId);
    }
  }

  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getSeverityEmoji(severity: Alert['severity']): string {
    const emojis = {
      low: '🟢',
      medium: '🟡',
      high: '🟠',
      critical: '🔴',
    };
    return emojis[severity];
  }
}

// Export singleton instance
export const skillGapAlerting = new SkillGapAlerting();

// Export class for testing
export { SkillGapAlerting };
