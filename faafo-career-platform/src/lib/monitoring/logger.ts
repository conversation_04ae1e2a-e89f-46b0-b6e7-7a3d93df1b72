/**
 * Comprehensive Logging System for Skill Gap Analyzer
 * Structured logging with multiple transports and log levels
 */

import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';

export interface LogContext {
  userId?: string;
  sessionId?: string;
  requestId?: string;
  feature?: string;
  action?: string;
  duration?: number;
  metadata?: Record<string, any>;
}

export interface SkillGapLogContext extends LogContext {
  skillCount?: number;
  careerPath?: string;
  analysisType?: string;
  errorCode?: string;
  performanceMetrics?: {
    responseTime: number;
    memoryUsage: number;
    cacheHit: boolean;
  };
}

class SkillGapLogger {
  private logger: winston.Logger;
  private isProduction: boolean;

  constructor() {
    this.isProduction = process.env.NODE_ENV === 'production';
    this.logger = this.createLogger();
  }

  private createLogger(): winston.Logger {
    const logFormat = winston.format.combine(
      winston.format.timestamp(),
      winston.format.errors({ stack: true }),
      winston.format.json(),
      winston.format.printf(({ timestamp, level, message, ...meta }) => {
        return JSON.stringify({
          timestamp,
          level,
          message,
          ...meta,
        });
      })
    );

    const transports: winston.transport[] = [];

    // Console transport for development
    if (!this.isProduction) {
      transports.push(
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            winston.format.simple()
          ),
        })
      );
    }

    // File transports for production
    if (this.isProduction) {
      // General application logs
      transports.push(
        new DailyRotateFile({
          filename: 'logs/skill-gap-%DATE%.log',
          datePattern: 'YYYY-MM-DD',
          maxSize: '20m',
          maxFiles: '14d',
          format: logFormat,
        })
      );

      // Error logs
      transports.push(
        new DailyRotateFile({
          filename: 'logs/skill-gap-error-%DATE%.log',
          datePattern: 'YYYY-MM-DD',
          level: 'error',
          maxSize: '20m',
          maxFiles: '30d',
          format: logFormat,
        })
      );

      // Performance logs
      transports.push(
        new DailyRotateFile({
          filename: 'logs/skill-gap-performance-%DATE%.log',
          datePattern: 'YYYY-MM-DD',
          maxSize: '20m',
          maxFiles: '7d',
          format: logFormat,
          level: 'info',
        })
      );
    }

    return winston.createLogger({
      level: this.isProduction ? 'info' : 'debug',
      format: logFormat,
      transports,
      defaultMeta: {
        service: 'skill-gap-analyzer',
        version: process.env.npm_package_version || '1.0.0',
      },
    });
  }

  /**
   * Log skill search events
   */
  logSkillSearch(context: SkillGapLogContext & { query: string; resultCount: number }) {
    this.logger.info('Skill search performed', {
      action: 'skill_search',
      query: context.query,
      resultCount: context.resultCount,
      userId: context.userId,
      sessionId: context.sessionId,
      duration: context.duration,
      performanceMetrics: context.performanceMetrics,
    });
  }

  /**
   * Log skill assessment events
   */
  logSkillAssessment(context: SkillGapLogContext & { assessmentData: any }) {
    this.logger.info('Skill assessment submitted', {
      action: 'skill_assessment',
      skillCount: context.skillCount,
      userId: context.userId,
      sessionId: context.sessionId,
      duration: context.duration,
      assessmentData: {
        skillCount: context.assessmentData?.length || 0,
        avgRating: this.calculateAverageRating(context.assessmentData),
      },
      performanceMetrics: context.performanceMetrics,
    });
  }

  /**
   * Log skill gap analysis events
   */
  logSkillGapAnalysis(context: SkillGapLogContext & { analysisResult: any }) {
    this.logger.info('Skill gap analysis completed', {
      action: 'skill_gap_analysis',
      analysisType: context.analysisType,
      careerPath: context.careerPath,
      skillCount: context.skillCount,
      userId: context.userId,
      sessionId: context.sessionId,
      duration: context.duration,
      analysisResult: {
        gapCount: context.analysisResult?.skillGaps?.length || 0,
        readinessScore: context.analysisResult?.readinessScore,
        recommendationCount: context.analysisResult?.recommendations?.length || 0,
      },
      performanceMetrics: context.performanceMetrics,
    });
  }

  /**
   * Log AI service interactions
   */
  logAIServiceCall(context: SkillGapLogContext & { 
    service: string; 
    prompt: string; 
    responseLength: number;
    tokensUsed?: number;
  }) {
    this.logger.info('AI service call', {
      action: 'ai_service_call',
      service: context.service,
      promptLength: context.prompt.length,
      responseLength: context.responseLength,
      tokensUsed: context.tokensUsed,
      userId: context.userId,
      sessionId: context.sessionId,
      duration: context.duration,
      performanceMetrics: context.performanceMetrics,
    });
  }

  /**
   * Log performance metrics
   */
  logPerformance(context: SkillGapLogContext & {
    endpoint: string;
    method: string;
    statusCode: number;
  }) {
    const level = context.duration && context.duration > 5000 ? 'warn' : 'info';
    
    this.logger.log(level, 'Performance metrics', {
      action: 'performance_metric',
      endpoint: context.endpoint,
      method: context.method,
      statusCode: context.statusCode,
      duration: context.duration,
      userId: context.userId,
      sessionId: context.sessionId,
      performanceMetrics: context.performanceMetrics,
    });
  }

  /**
   * Log errors with context
   */
  logError(error: Error, context: SkillGapLogContext) {
    this.logger.error('Skill gap analyzer error', {
      action: 'error',
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
      errorCode: context.errorCode,
      feature: context.feature,
      userId: context.userId,
      sessionId: context.sessionId,
      requestId: context.requestId,
      metadata: context.metadata,
    });
  }

  /**
   * Log security events
   */
  logSecurityEvent(context: SkillGapLogContext & {
    eventType: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    details: string;
  }) {
    this.logger.warn('Security event', {
      action: 'security_event',
      eventType: context.eventType,
      severity: context.severity,
      details: context.details,
      userId: context.userId,
      sessionId: context.sessionId,
      requestId: context.requestId,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Log user behavior analytics
   */
  logUserBehavior(context: SkillGapLogContext & {
    event: string;
    properties: Record<string, any>;
  }) {
    this.logger.info('User behavior event', {
      action: 'user_behavior',
      event: context.event,
      properties: context.properties,
      userId: context.userId,
      sessionId: context.sessionId,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Log business metrics
   */
  logBusinessMetric(context: SkillGapLogContext & {
    metric: string;
    value: number;
    unit: string;
  }) {
    this.logger.info('Business metric', {
      action: 'business_metric',
      metric: context.metric,
      value: context.value,
      unit: context.unit,
      userId: context.userId,
      sessionId: context.sessionId,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Log system health metrics
   */
  logSystemHealth(metrics: {
    memoryUsage: number;
    cpuUsage: number;
    activeConnections: number;
    responseTime: number;
    errorRate: number;
  }) {
    this.logger.info('System health metrics', {
      action: 'system_health',
      metrics,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Helper methods
   */
  private calculateAverageRating(assessments: any[]): number {
    if (!assessments || assessments.length === 0) return 0;
    
    const total = assessments.reduce((sum, assessment) => {
      return sum + (assessment.selfRating || 0);
    }, 0);
    
    return Math.round((total / assessments.length) * 100) / 100;
  }

  /**
   * Create child logger with context
   */
  child(context: Partial<SkillGapLogContext>): SkillGapLogger {
    const childLogger = new SkillGapLogger();
    childLogger.logger = this.logger.child(context);
    return childLogger;
  }

  /**
   * Get raw winston logger for advanced usage
   */
  getRawLogger(): winston.Logger {
    return this.logger;
  }

  /**
   * Flush logs (useful for testing)
   */
  async flush(): Promise<void> {
    return new Promise((resolve) => {
      this.logger.on('finish', resolve);
      this.logger.end();
    });
  }
}

// Export singleton instance
export const skillGapLogger = new SkillGapLogger();

// Export class for testing
export { SkillGapLogger };

// Export log levels for convenience
export const LogLevels = {
  ERROR: 'error',
  WARN: 'warn',
  INFO: 'info',
  DEBUG: 'debug',
} as const;
