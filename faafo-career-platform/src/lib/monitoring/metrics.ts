/**
 * Comprehensive Metrics Collection System for Skill Gap Analyzer
 * Real-time metrics collection, aggregation, and alerting
 */

import { EventEmitter } from 'events';
import { skillGapLogger } from './logger';

export interface MetricData {
  name: string;
  value: number;
  unit: string;
  timestamp: number;
  tags?: Record<string, string>;
  metadata?: Record<string, any>;
}

export interface AlertRule {
  id: string;
  metricName: string;
  condition: 'gt' | 'lt' | 'eq' | 'gte' | 'lte';
  threshold: number;
  duration: number; // milliseconds
  severity: 'low' | 'medium' | 'high' | 'critical';
  enabled: boolean;
  lastTriggered?: number;
  cooldown: number; // milliseconds
}

export interface SystemMetrics {
  responseTime: number;
  throughput: number;
  errorRate: number;
  memoryUsage: number;
  cpuUsage: number;
  activeUsers: number;
  cacheHitRate: number;
  aiServiceLatency: number;
  databaseConnections: number;
}

class SkillGapMetrics extends EventEmitter {
  private metrics: Map<string, MetricData[]> = new Map();
  private alertRules: Map<string, AlertRule> = new Map();
  private aggregationInterval: NodeJS.Timeout | null = null;
  private maxMetricsPerType = 1000; // Keep last 1000 data points per metric
  private isCollecting = false;

  constructor() {
    super();
    this.setupDefaultAlerts();
    this.startAggregation();
  }

  /**
   * Record a metric value
   */
  recordMetric(data: Omit<MetricData, 'timestamp'>): void {
    const metric: MetricData = {
      ...data,
      timestamp: Date.now(),
    };

    // Store metric
    if (!this.metrics.has(data.name)) {
      this.metrics.set(data.name, []);
    }

    const metricArray = this.metrics.get(data.name)!;
    metricArray.push(metric);

    // Keep only recent metrics
    if (metricArray.length > this.maxMetricsPerType) {
      metricArray.shift();
    }

    // Check alert rules
    this.checkAlerts(data.name, data.value);

    // Emit metric event
    this.emit('metric', metric);

    // Log metric
    skillGapLogger.logBusinessMetric({
      metric: data.name,
      value: data.value,
      unit: data.unit,
      metadata: data.metadata,
    });
  }

  /**
   * Record skill search metrics
   */
  recordSkillSearch(duration: number, resultCount: number, cacheHit: boolean): void {
    this.recordMetric({
      name: 'skill_search_duration',
      value: duration,
      unit: 'ms',
      tags: { cache_hit: cacheHit.toString() },
    });

    this.recordMetric({
      name: 'skill_search_results',
      value: resultCount,
      unit: 'count',
    });

    this.recordMetric({
      name: 'skill_search_cache_hit_rate',
      value: cacheHit ? 1 : 0,
      unit: 'boolean',
    });
  }

  /**
   * Record skill assessment metrics
   */
  recordSkillAssessment(duration: number, skillCount: number, userId: string): void {
    this.recordMetric({
      name: 'skill_assessment_duration',
      value: duration,
      unit: 'ms',
      tags: { user_id: userId },
    });

    this.recordMetric({
      name: 'skill_assessment_count',
      value: skillCount,
      unit: 'count',
    });

    this.recordMetric({
      name: 'skill_assessments_completed',
      value: 1,
      unit: 'count',
    });
  }

  /**
   * Record AI service metrics
   */
  recordAIServiceCall(
    service: string,
    duration: number,
    tokensUsed: number,
    success: boolean
  ): void {
    this.recordMetric({
      name: 'ai_service_duration',
      value: duration,
      unit: 'ms',
      tags: { service, success: success.toString() },
    });

    this.recordMetric({
      name: 'ai_service_tokens',
      value: tokensUsed,
      unit: 'count',
      tags: { service },
    });

    this.recordMetric({
      name: 'ai_service_calls',
      value: 1,
      unit: 'count',
      tags: { service, success: success.toString() },
    });
  }

  /**
   * Record system performance metrics
   */
  recordSystemMetrics(metrics: SystemMetrics): void {
    Object.entries(metrics).forEach(([key, value]) => {
      this.recordMetric({
        name: `system_${key}`,
        value,
        unit: this.getUnitForSystemMetric(key),
      });
    });
  }

  /**
   * Record error metrics
   */
  recordError(errorType: string, endpoint: string, statusCode: number): void {
    this.recordMetric({
      name: 'error_count',
      value: 1,
      unit: 'count',
      tags: { error_type: errorType, endpoint, status_code: statusCode.toString() },
    });

    this.recordMetric({
      name: 'error_rate',
      value: 1,
      unit: 'rate',
      tags: { endpoint },
    });
  }

  /**
   * Record user behavior metrics
   */
  recordUserBehavior(event: string, userId: string, properties: Record<string, any>): void {
    this.recordMetric({
      name: 'user_behavior',
      value: 1,
      unit: 'count',
      tags: { event, user_id: userId },
      metadata: properties,
    });
  }

  /**
   * Get metric statistics
   */
  getMetricStats(metricName: string, timeWindow?: number): {
    count: number;
    min: number;
    max: number;
    avg: number;
    p50: number;
    p95: number;
    p99: number;
  } | null {
    const metrics = this.metrics.get(metricName);
    if (!metrics || metrics.length === 0) return null;

    const now = Date.now();
    const windowStart = timeWindow ? now - timeWindow : 0;
    const filteredMetrics = metrics.filter(m => m.timestamp >= windowStart);

    if (filteredMetrics.length === 0) return null;

    const values = filteredMetrics.map(m => m.value).sort((a, b) => a - b);
    const count = values.length;
    const sum = values.reduce((a, b) => a + b, 0);

    return {
      count,
      min: values[0],
      max: values[count - 1],
      avg: sum / count,
      p50: values[Math.floor(count * 0.5)],
      p95: values[Math.floor(count * 0.95)],
      p99: values[Math.floor(count * 0.99)],
    };
  }

  /**
   * Get current system health
   */
  getSystemHealth(): {
    status: 'healthy' | 'warning' | 'critical';
    metrics: Record<string, any>;
    alerts: AlertRule[];
  } {
    const recentMetrics = this.getRecentMetrics(60000); // Last minute
    const activeAlerts = Array.from(this.alertRules.values()).filter(
      rule => rule.enabled && this.isAlertActive(rule)
    );

    let status: 'healthy' | 'warning' | 'critical' = 'healthy';
    if (activeAlerts.some(alert => alert.severity === 'critical')) {
      status = 'critical';
    } else if (activeAlerts.some(alert => alert.severity === 'high' || alert.severity === 'medium')) {
      status = 'warning';
    }

    return {
      status,
      metrics: recentMetrics,
      alerts: activeAlerts,
    };
  }

  /**
   * Add or update alert rule
   */
  addAlertRule(rule: AlertRule): void {
    this.alertRules.set(rule.id, rule);
  }

  /**
   * Remove alert rule
   */
  removeAlertRule(ruleId: string): void {
    this.alertRules.delete(ruleId);
  }

  /**
   * Get all metrics for export
   */
  exportMetrics(timeWindow?: number): Record<string, MetricData[]> {
    const exported: Record<string, MetricData[]> = {};
    const now = Date.now();
    const windowStart = timeWindow ? now - timeWindow : 0;

    this.metrics.forEach((metrics, name) => {
      exported[name] = metrics.filter(m => m.timestamp >= windowStart);
    });

    return exported;
  }

  /**
   * Clear all metrics
   */
  clearMetrics(): void {
    this.metrics.clear();
  }

  /**
   * Start collecting system metrics
   */
  startSystemMetricsCollection(): void {
    if (this.isCollecting) return;

    this.isCollecting = true;
    const collectInterval = setInterval(() => {
      this.collectSystemMetrics();
    }, 30000); // Every 30 seconds

    // Store interval for cleanup
    (this as any).collectInterval = collectInterval;
  }

  /**
   * Stop collecting system metrics
   */
  stopSystemMetricsCollection(): void {
    if ((this as any).collectInterval) {
      clearInterval((this as any).collectInterval);
      (this as any).collectInterval = null;
    }
    this.isCollecting = false;
  }

  /**
   * Private methods
   */
  private setupDefaultAlerts(): void {
    const defaultAlerts: AlertRule[] = [
      {
        id: 'high_response_time',
        metricName: 'skill_search_duration',
        condition: 'gt',
        threshold: 2000,
        duration: 60000,
        severity: 'high',
        enabled: true,
        cooldown: 300000, // 5 minutes
      },
      {
        id: 'high_error_rate',
        metricName: 'error_rate',
        condition: 'gt',
        threshold: 0.05, // 5%
        duration: 120000,
        severity: 'critical',
        enabled: true,
        cooldown: 600000, // 10 minutes
      },
      {
        id: 'ai_service_slow',
        metricName: 'ai_service_duration',
        condition: 'gt',
        threshold: 10000,
        duration: 60000,
        severity: 'medium',
        enabled: true,
        cooldown: 300000,
      },
      {
        id: 'low_cache_hit_rate',
        metricName: 'skill_search_cache_hit_rate',
        condition: 'lt',
        threshold: 0.7, // 70%
        duration: 300000,
        severity: 'medium',
        enabled: true,
        cooldown: 600000,
      },
    ];

    defaultAlerts.forEach(alert => this.addAlertRule(alert));
  }

  private checkAlerts(metricName: string, value: number): void {
    this.alertRules.forEach(rule => {
      if (rule.metricName === metricName && rule.enabled) {
        const shouldTrigger = this.evaluateAlertCondition(rule, value);
        
        if (shouldTrigger && this.canTriggerAlert(rule)) {
          this.triggerAlert(rule, value);
        }
      }
    });
  }

  private evaluateAlertCondition(rule: AlertRule, value: number): boolean {
    switch (rule.condition) {
      case 'gt': return value > rule.threshold;
      case 'gte': return value >= rule.threshold;
      case 'lt': return value < rule.threshold;
      case 'lte': return value <= rule.threshold;
      case 'eq': return value === rule.threshold;
      default: return false;
    }
  }

  private canTriggerAlert(rule: AlertRule): boolean {
    if (!rule.lastTriggered) return true;
    return Date.now() - rule.lastTriggered > rule.cooldown;
  }

  private triggerAlert(rule: AlertRule, value: number): void {
    rule.lastTriggered = Date.now();
    
    const alert = {
      ruleId: rule.id,
      metricName: rule.metricName,
      severity: rule.severity,
      value,
      threshold: rule.threshold,
      timestamp: Date.now(),
    };

    this.emit('alert', alert);
    
    skillGapLogger.logSecurityEvent({
      eventType: 'metric_alert',
      severity: rule.severity,
      details: `Alert ${rule.id}: ${rule.metricName} ${rule.condition} ${rule.threshold} (current: ${value})`,
    });
  }

  private isAlertActive(rule: AlertRule): boolean {
    if (!rule.lastTriggered) return false;
    return Date.now() - rule.lastTriggered < rule.duration;
  }

  private getRecentMetrics(timeWindow: number): Record<string, any> {
    const recent: Record<string, any> = {};
    
    this.metrics.forEach((metrics, name) => {
      const stats = this.getMetricStats(name, timeWindow);
      if (stats) {
        recent[name] = stats;
      }
    });

    return recent;
  }

  private getUnitForSystemMetric(key: string): string {
    const units: Record<string, string> = {
      responseTime: 'ms',
      throughput: 'req/s',
      errorRate: 'percentage',
      memoryUsage: 'percentage',
      cpuUsage: 'percentage',
      activeUsers: 'count',
      cacheHitRate: 'percentage',
      aiServiceLatency: 'ms',
      databaseConnections: 'count',
    };
    return units[key] || 'count';
  }

  private async collectSystemMetrics(): Promise<void> {
    try {
      const metrics: SystemMetrics = {
        responseTime: await this.getAverageResponseTime(),
        throughput: await this.getCurrentThroughput(),
        errorRate: await this.getCurrentErrorRate(),
        memoryUsage: this.getMemoryUsage(),
        cpuUsage: await this.getCPUUsage(),
        activeUsers: await this.getActiveUsers(),
        cacheHitRate: await this.getCacheHitRate(),
        aiServiceLatency: await this.getAIServiceLatency(),
        databaseConnections: await this.getDatabaseConnections(),
      };

      this.recordSystemMetrics(metrics);
      skillGapLogger.logSystemHealth(metrics);
    } catch (error) {
      skillGapLogger.logError(error as Error, {
        feature: 'metrics_collection',
        action: 'collect_system_metrics',
      });
    }
  }

  private startAggregation(): void {
    this.aggregationInterval = setInterval(() => {
      this.aggregateMetrics();
    }, 60000); // Every minute
  }

  private aggregateMetrics(): void {
    // Aggregate metrics for reporting
    const aggregated = this.getRecentMetrics(60000);
    this.emit('aggregated_metrics', aggregated);
  }

  // Placeholder methods for system metrics collection
  private async getAverageResponseTime(): Promise<number> {
    const stats = this.getMetricStats('skill_search_duration', 60000);
    return stats?.avg || 0;
  }

  private async getCurrentThroughput(): Promise<number> {
    const stats = this.getMetricStats('skill_assessments_completed', 60000);
    return stats ? stats.count / 60 : 0; // per second
  }

  private async getCurrentErrorRate(): Promise<number> {
    const errorStats = this.getMetricStats('error_count', 60000);
    const totalStats = this.getMetricStats('skill_assessments_completed', 60000);
    
    if (!errorStats || !totalStats || totalStats.count === 0) return 0;
    return (errorStats.count / totalStats.count) * 100;
  }

  private getMemoryUsage(): number {
    const used = process.memoryUsage();
    return (used.heapUsed / used.heapTotal) * 100;
  }

  private async getCPUUsage(): Promise<number> {
    // Simplified CPU usage calculation
    return Math.random() * 100; // Placeholder
  }

  private async getActiveUsers(): Promise<number> {
    // Count unique users in recent metrics
    const recentMetrics = this.getRecentMetrics(300000); // 5 minutes
    const userIds = new Set<string>();
    
    Object.values(recentMetrics).forEach((metricArray: any) => {
      if (Array.isArray(metricArray)) {
        metricArray.forEach((metric: any) => {
          if (metric.tags?.user_id) {
            userIds.add(metric.tags.user_id);
          }
        });
      }
    });
    
    return userIds.size;
  }

  private async getCacheHitRate(): Promise<number> {
    const stats = this.getMetricStats('skill_search_cache_hit_rate', 60000);
    return stats ? stats.avg * 100 : 0;
  }

  private async getAIServiceLatency(): Promise<number> {
    const stats = this.getMetricStats('ai_service_duration', 60000);
    return stats?.avg || 0;
  }

  private async getDatabaseConnections(): Promise<number> {
    // Placeholder - would integrate with actual database monitoring
    return Math.floor(Math.random() * 20) + 5;
  }
}

// Export singleton instance
export const skillGapMetrics = new SkillGapMetrics();

// Export class for testing
export { SkillGapMetrics };
