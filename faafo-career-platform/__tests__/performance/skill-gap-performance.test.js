/**
 * Performance Tests for Skill Gap Analyzer
 * Validates performance requirements and benchmarks
 */

const SkillGapLoadTester = require('../../scripts/performance/load-test-skill-gap');
const PerformanceMonitor = require('../../scripts/performance/performance-monitor');

describe('Skill Gap Analyzer Performance Tests', () => {
  let loadTester;
  let performanceMonitor;

  beforeAll(() => {
    loadTester = new SkillGapLoadTester({
      baseUrl: process.env.TEST_BASE_URL || 'http://localhost:3000',
      concurrentUsers: 5,
      testDuration: 30000, // 30 seconds for tests
      rampUpTime: 5000,
    });

    performanceMonitor = new PerformanceMonitor({
      monitoringInterval: 1000,
      responseTimeThreshold: 2000,
      errorRateThreshold: 5,
    });
  });

  describe('Response Time Requirements', () => {
    it('should handle skill search within 500ms', async () => {
      const startTime = Date.now();
      const result = await loadTester.testSkillSearch('JavaScript');
      const responseTime = Date.now() - startTime;

      expect(result.success).toBe(true);
      expect(responseTime).toBeLessThan(500);
    }, 10000);

    it('should handle skill assessment within 1000ms', async () => {
      const testData = loadTester.generateTestData();
      const startTime = Date.now();
      
      const result = await loadTester.testSkillAssessment(
        testData.assessments.slice(0, 3), 
        'test-user'
      );
      const responseTime = Date.now() - startTime;

      expect(result.success).toBe(true);
      expect(responseTime).toBeLessThan(1000);
    }, 15000);

    it('should handle comprehensive analysis within 10000ms', async () => {
      const testData = loadTester.generateTestData();
      const startTime = Date.now();
      
      const result = await loadTester.testComprehensiveAnalysis(testData, 'test-user');
      const responseTime = Date.now() - startTime;

      expect(result.success).toBe(true);
      expect(responseTime).toBeLessThan(10000);
    }, 20000);
  });

  describe('Throughput Requirements', () => {
    it('should handle concurrent skill searches', async () => {
      const concurrentRequests = 10;
      const promises = [];

      for (let i = 0; i < concurrentRequests; i++) {
        promises.push(loadTester.testSkillSearch(`skill-${i}`));
      }

      const results = await Promise.all(promises);
      const successfulRequests = results.filter(r => r.success).length;
      const successRate = (successfulRequests / concurrentRequests) * 100;

      expect(successRate).toBeGreaterThan(95); // 95% success rate
    }, 15000);

    it('should maintain performance under load', async () => {
      const results = await loadTester.runLoadTest();

      expect(results.errorRate).toBeLessThan(5); // Less than 5% error rate
      expect(results.averageResponseTime).toBeLessThan(2000); // Less than 2s average
      expect(results.throughput).toBeGreaterThan(1); // At least 1 req/s
    }, 60000);
  });

  describe('Memory and Resource Usage', () => {
    it('should not exceed memory limits during load test', async () => {
      const initialMemory = process.memoryUsage();
      
      // Run a smaller load test
      const smallLoadTester = new SkillGapLoadTester({
        baseUrl: process.env.TEST_BASE_URL || 'http://localhost:3000',
        concurrentUsers: 3,
        testDuration: 10000,
        rampUpTime: 2000,
      });

      await smallLoadTester.runLoadTest();
      
      const finalMemory = process.memoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      const memoryIncreasePercent = (memoryIncrease / initialMemory.heapUsed) * 100;

      // Memory increase should be reasonable (less than 50%)
      expect(memoryIncreasePercent).toBeLessThan(50);
    }, 30000);

    it('should handle garbage collection efficiently', async () => {
      const initialMemory = process.memoryUsage();
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      // Run multiple small operations
      for (let i = 0; i < 100; i++) {
        await loadTester.testSkillSearch(`test-${i}`);
      }

      // Force garbage collection again
      if (global.gc) {
        global.gc();
      }

      const finalMemory = process.memoryUsage();
      const memoryDiff = finalMemory.heapUsed - initialMemory.heapUsed;

      // Memory should not grow significantly after GC
      expect(memoryDiff).toBeLessThan(10 * 1024 * 1024); // Less than 10MB
    }, 20000);
  });

  describe('Error Handling Performance', () => {
    it('should handle invalid requests gracefully', async () => {
      const startTime = Date.now();
      
      // Test with invalid data
      const result = await loadTester.testSkillAssessment([], 'invalid-user');
      const responseTime = Date.now() - startTime;

      // Should respond quickly even for errors
      expect(responseTime).toBeLessThan(1000);
    }, 10000);

    it('should maintain performance during error conditions', async () => {
      const requests = [];
      
      // Mix of valid and invalid requests
      for (let i = 0; i < 20; i++) {
        if (i % 3 === 0) {
          // Invalid request
          requests.push(loadTester.testSkillSearch(''));
        } else {
          // Valid request
          requests.push(loadTester.testSkillSearch('JavaScript'));
        }
      }

      const results = await Promise.all(requests);
      const responseTimes = results.map(r => r.responseTime);
      const averageResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;

      // Average response time should still be reasonable
      expect(averageResponseTime).toBeLessThan(1000);
    }, 15000);
  });

  describe('Scalability Tests', () => {
    it('should scale linearly with user load', async () => {
      const userCounts = [1, 2, 5];
      const results = [];

      for (const userCount of userCounts) {
        const tester = new SkillGapLoadTester({
          baseUrl: process.env.TEST_BASE_URL || 'http://localhost:3000',
          concurrentUsers: userCount,
          testDuration: 10000,
          rampUpTime: 2000,
        });

        const result = await tester.runLoadTest();
        results.push({
          users: userCount,
          throughput: result.throughput,
          averageResponseTime: result.averageResponseTime,
          errorRate: result.errorRate,
        });
      }

      // Throughput should increase with more users (up to a point)
      expect(results[1].throughput).toBeGreaterThan(results[0].throughput * 0.8);
      
      // Error rate should remain low
      results.forEach(result => {
        expect(result.errorRate).toBeLessThan(10);
      });
    }, 60000);
  });

  describe('Performance Monitoring', () => {
    it('should detect performance degradation', async () => {
      performanceMonitor.start();
      
      // Simulate some load
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      const report = performanceMonitor.stop();
      
      expect(report.summary.monitoringDuration).toBeDefined();
      expect(report.metrics).toBeDefined();
      expect(report.recommendations).toBeDefined();
    }, 10000);

    it('should generate meaningful performance recommendations', async () => {
      // Create a monitor with strict thresholds
      const strictMonitor = new PerformanceMonitor({
        monitoringInterval: 1000,
        responseTimeThreshold: 100, // Very strict
        errorRateThreshold: 1,
      });

      strictMonitor.start();
      await new Promise(resolve => setTimeout(resolve, 3000));
      const report = strictMonitor.stop();

      // Should generate recommendations due to strict thresholds
      expect(report.recommendations.length).toBeGreaterThan(0);
      expect(report.recommendations[0]).toHaveProperty('type');
      expect(report.recommendations[0]).toHaveProperty('priority');
      expect(report.recommendations[0]).toHaveProperty('message');
    }, 10000);
  });

  describe('Performance Benchmarks', () => {
    it('should meet skill search performance benchmark', async () => {
      const iterations = 50;
      const responseTimes = [];

      for (let i = 0; i < iterations; i++) {
        const startTime = Date.now();
        await loadTester.testSkillSearch('React');
        const responseTime = Date.now() - startTime;
        responseTimes.push(responseTime);
      }

      const averageResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
      const p95ResponseTime = responseTimes.sort((a, b) => a - b)[Math.floor(responseTimes.length * 0.95)];

      expect(averageResponseTime).toBeLessThan(300); // Average < 300ms
      expect(p95ResponseTime).toBeLessThan(500); // 95th percentile < 500ms
    }, 30000);

    it('should meet comprehensive analysis performance benchmark', async () => {
      const testData = loadTester.generateTestData();
      const iterations = 5;
      const responseTimes = [];

      for (let i = 0; i < iterations; i++) {
        const startTime = Date.now();
        await loadTester.testComprehensiveAnalysis(testData, `benchmark-user-${i}`);
        const responseTime = Date.now() - startTime;
        responseTimes.push(responseTime);
      }

      const averageResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;

      expect(averageResponseTime).toBeLessThan(8000); // Average < 8s
    }, 60000);
  });

  afterAll(() => {
    // Cleanup
    if (performanceMonitor.isMonitoring) {
      performanceMonitor.stop();
    }
  });
});
