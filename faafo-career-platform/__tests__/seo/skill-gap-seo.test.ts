/**
 * Tests for Skill Gap Analyzer SEO functionality
 */

import { 
  generateSkillGapSEO, 
  generateStructuredData, 
  generateBreadcrumbData, 
  generateFAQData,
  generateSEOUrl 
} from '@/lib/seo/skill-gap-seo';

describe('Skill Gap Analyzer SEO', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    process.env = {
      ...originalEnv,
      NEXT_PUBLIC_BASE_URL: 'https://test.faafo.com'
    };
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  describe('generateSkillGapSEO', () => {
    it('should generate main page SEO metadata', () => {
      const seo = generateSkillGapSEO('main');

      expect(seo.title).toContain('Skill Gap Analyzer');
      expect(seo.description).toContain('AI analysis');
      expect(seo.keywords).toContain('skill gap analysis');
      expect(seo.openGraph?.title).toContain('AI-Powered Skill Gap Analysis');
      expect(seo.openGraph?.url).toBe('https://test.faafo.com/skills/gap-analyzer');
      expect(seo.robots?.index).toBe(true);
      expect(seo.robots?.follow).toBe(true);
    });

    it('should generate assessment page SEO metadata', () => {
      const seo = generateSkillGapSEO('assessment');

      expect(seo.title).toContain('Skill Assessment');
      expect(seo.description).toContain('skill assessment');
      expect(seo.keywords).toContain('skill assessment');
      expect(seo.openGraph?.url).toBe('https://test.faafo.com/skills/gap-analyzer?tab=assess');
    });

    it('should generate analysis page SEO with career path', () => {
      const seo = generateSkillGapSEO('analysis', { 
        careerPath: 'Software Engineer',
        skillCount: 15 
      });
      
      expect(seo.title).toContain('for Software Engineer');
      expect(seo.description).toContain('for Software Engineer');
      expect(seo.keywords).toContain('software engineer');
    });

    it('should generate results page SEO with completion rate', () => {
      const seo = generateSkillGapSEO('results', {
        skillCount: 20,
        completionRate: 75
      });

      expect(seo.title).toContain('75% Ready');
      expect(seo.description).toContain('(20 skills)');
      expect(seo.robots?.index).toBe(false); // Results are user-specific
    });
  });

  describe('generateStructuredData', () => {
    it('should generate assessment structured data', () => {
      const data = generateStructuredData({
        type: 'assessment',
        data: {}
      });
      
      expect(data['@type']).toBe('Assessment');
      expect(data['name']).toBe('Professional Skill Assessment');
      expect(data['educationalLevel']).toBe('Professional');
    });

    it('should generate analysis structured data', () => {
      const data = generateStructuredData({
        type: 'analysis',
        data: { careerPath: 'Data Scientist' }
      });
      
      expect(data['@type']).toBe('AnalysisNewsArticle');
      expect(data['headline']).toBe('Skill Gap Analysis Results');
      expect(data['author']['@type']).toBe('Organization');
    });

    it('should generate results structured data', () => {
      const data = generateStructuredData({
        type: 'results',
        data: { careerPath: 'Product Manager' }
      });
      
      expect(data['@type']).toBe('Report');
      expect(data['about']).toBe('Product Manager');
      expect(data['dateCreated']).toBeDefined();
    });

    it('should generate learning path structured data', () => {
      const data = generateStructuredData({
        type: 'learning-path',
        data: { 
          level: 'Intermediate',
          skills: ['JavaScript', 'React', 'Node.js'],
          duration: '6 months'
        }
      });
      
      expect(data['@type']).toBe('LearningResource');
      expect(data['teaches']).toEqual(['JavaScript', 'React', 'Node.js']);
      expect(data['timeRequired']).toBe('6 months');
    });
  });

  describe('generateBreadcrumbData', () => {
    it('should generate main page breadcrumbs', () => {
      const breadcrumbs = generateBreadcrumbData('main');
      
      expect(breadcrumbs['@type']).toBe('BreadcrumbList');
      expect(breadcrumbs['itemListElement']).toHaveLength(3);
      expect(breadcrumbs['itemListElement'][0]['name']).toBe('Home');
      expect(breadcrumbs['itemListElement'][2]['name']).toBe('Gap Analyzer');
    });

    it('should generate assessment page breadcrumbs', () => {
      const breadcrumbs = generateBreadcrumbData('assessment');
      
      expect(breadcrumbs['itemListElement']).toHaveLength(4);
      expect(breadcrumbs['itemListElement'][3]['name']).toBe('Skill Assessment');
      expect(breadcrumbs['itemListElement'][3]['item']).toContain('?tab=assessment');
    });
  });

  describe('generateFAQData', () => {
    it('should generate FAQ structured data', () => {
      const faq = generateFAQData();
      
      expect(faq['@type']).toBe('FAQPage');
      expect(faq['mainEntity']).toBeInstanceOf(Array);
      expect(faq['mainEntity'].length).toBeGreaterThan(0);
      
      const firstQuestion = faq['mainEntity'][0];
      expect(firstQuestion['@type']).toBe('Question');
      expect(firstQuestion['name']).toContain('skill gap analysis');
      expect(firstQuestion['acceptedAnswer']['@type']).toBe('Answer');
    });
  });

  describe('generateSEOUrl', () => {
    it('should generate main page URL', () => {
      const url = generateSEOUrl('main');
      expect(url).toBe('https://test.faafo.com/skills/gap-analyzer');
    });

    it('should generate assessment page URL', () => {
      const url = generateSEOUrl('assessment');
      expect(url).toBe('https://test.faafo.com/skills/gap-analyzer?tab=assessment');
    });

    it('should generate URL with career path parameter', () => {
      const url = generateSEOUrl('analysis', { 
        careerPath: 'Full Stack Developer' 
      });
      expect(url).toBe('https://test.faafo.com/skills/gap-analyzer?tab=analysis&career=full-stack-developer');
    });

    it('should generate URL with multiple parameters', () => {
      const url = generateSEOUrl('results', { 
        careerPath: 'Data Scientist',
        skillId: 'python',
        analysisId: 'abc123'
      });
      expect(url).toContain('career=data-scientist');
      expect(url).toContain('skill=python');
      expect(url).toContain('analysis=abc123');
    });

    it('should sanitize career path for URL', () => {
      const url = generateSEOUrl('main', { 
        careerPath: 'Senior Software Engineer (Full-Stack)' 
      });
      expect(url).toContain('career=senior-software-engineer-full-stack');
    });
  });

  describe('SEO best practices', () => {
    it('should have appropriate title lengths', () => {
      const pages = ['main', 'assessment', 'analysis', 'results'] as const;
      
      pages.forEach(page => {
        const seo = generateSkillGapSEO(page);
        const title = typeof seo.title === 'string' ? seo.title : seo.title?.default || '';
        
        expect(title.length).toBeGreaterThan(10);
        expect(title.length).toBeLessThan(60); // Google's recommended limit
      });
    });

    it('should have appropriate description lengths', () => {
      const pages = ['main', 'assessment', 'analysis', 'results'] as const;
      
      pages.forEach(page => {
        const seo = generateSkillGapSEO(page);
        
        if (seo.description) {
          expect(seo.description.length).toBeGreaterThan(50);
          expect(seo.description.length).toBeLessThan(160); // Google's recommended limit
        }
      });
    });

    it('should include relevant keywords', () => {
      const seo = generateSkillGapSEO('main');
      const keywords = seo.keywords?.toLowerCase() || '';
      
      expect(keywords).toContain('skill');
      expect(keywords).toContain('career');
      expect(keywords).toContain('analysis');
      expect(keywords).toContain('assessment');
    });

    it('should have proper Open Graph data', () => {
      const seo = generateSkillGapSEO('main');
      
      expect(seo.openGraph?.title).toBeDefined();
      expect(seo.openGraph?.description).toBeDefined();
      expect(seo.openGraph?.type).toBe('website');
      expect(seo.openGraph?.url).toBeDefined();
      expect(seo.openGraph?.images).toBeDefined();
    });

    it('should have proper Twitter Card data', () => {
      const seo = generateSkillGapSEO('main');
      
      expect(seo.twitter?.card).toBe('summary_large_image');
      expect(seo.twitter?.title).toBeDefined();
      expect(seo.twitter?.description).toBeDefined();
      expect(seo.twitter?.images).toBeDefined();
    });
  });
});
